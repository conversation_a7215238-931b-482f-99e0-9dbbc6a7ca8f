<?php
/**
 * Admin Users Management
 * Multi-Tenant Administrative Management System
 */

define('ADMIN_SYSTEM', true);
require_once '../../config/admin_config.php';

// Require admin login and permission
requireAdminLogin();
requireAdminPermission('users', 'view');

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $userId = (int)($_POST['user_id'] ?? 0);
    
    try {
        $pdo = getAdminDBConnection();
        
        switch ($action) {
            case 'suspend_user':
                if (hasAdminPermission('users', 'edit')) {
                    $stmt = $pdo->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
                    $stmt->execute([$userId]);
                    
                    logAdminActivity('suspend_user', 'users', $userId, "تم إيقاف المستخدم");
                    
                    if (isAjaxRequest()) {
                        echo json_encode(['success' => true, 'message' => 'تم إيقاف المستخدم بنجاح']);
                        exit;
                    }
                }
                break;
                
            case 'activate_user':
                if (hasAdminPermission('users', 'edit')) {
                    $stmt = $pdo->prepare("UPDATE users SET is_active = 1 WHERE id = ?");
                    $stmt->execute([$userId]);
                    
                    logAdminActivity('activate_user', 'users', $userId, "تم تفعيل المستخدم");
                    
                    if (isAjaxRequest()) {
                        echo json_encode(['success' => true, 'message' => 'تم تفعيل المستخدم بنجاح']);
                        exit;
                    }
                }
                break;
                
            case 'delete_user':
                if (hasAdminPermission('users', 'delete')) {
                    // Get user data before deletion for logging
                    $stmt = $pdo->prepare("SELECT username, email FROM users WHERE id = ?");
                    $stmt->execute([$userId]);
                    $userData = $stmt->fetch();
                    
                    // Delete user (cascade will handle related data)
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$userId]);
                    
                    logAdminActivity('delete_user', 'users', $userId, 
                        "تم حذف المستخدم: {$userData['username']} ({$userData['email']})");
                    
                    if (isAjaxRequest()) {
                        echo json_encode(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);
                        exit;
                    }
                }
                break;
        }
    } catch (Exception $e) {
        logAdminError("User action error: " . $e->getMessage());
        if (isAjaxRequest()) {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
            exit;
        }
    }
}

// Get filters
$search = sanitizeAdminInput($_GET['search'] ?? '');
$status = sanitizeAdminInput($_GET['status'] ?? '');
$plan = sanitizeAdminInput($_GET['plan'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 25;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR company_name LIKE ?)";
    $searchTerm = "%{$search}%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($status !== '') {
    $whereConditions[] = "is_active = ?";
    $params[] = $status;
}

if (!empty($plan)) {
    $whereConditions[] = "subscription_plan = ?";
    $params[] = $plan;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

try {
    $pdo = getAdminDBConnection();
    
    // Get total count
    $countQuery = "SELECT COUNT(*) FROM users {$whereClause}";
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($params);
    $totalUsers = $stmt->fetchColumn();
    
    // Get users
    $query = "
        SELECT u.*, 
               (SELECT COUNT(*) FROM invoices WHERE user_id = u.id) as invoice_count,
               (SELECT COUNT(*) FROM clients WHERE user_id = u.id) as client_count,
               (SELECT SUM(total_amount) FROM invoices WHERE user_id = u.id AND status = 'paid') as total_revenue
        FROM users u 
        {$whereClause}
        ORDER BY u.created_at DESC 
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get statistics
    $stats = $pdo->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN subscription_plan = 'free' THEN 1 END) as free_users,
            COUNT(CASE WHEN subscription_plan = 'premium' THEN 1 END) as premium_users,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
        FROM users
    ")->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    logAdminError("Users page error: " . $e->getMessage());
    $users = [];
    $totalUsers = 0;
    $stats = ['total_users' => 0, 'active_users' => 0, 'free_users' => 0, 'premium_users' => 0, 'new_today' => 0];
}

$totalPages = ceil($totalUsers / $limit);

$pageTitle = 'إدارة المستخدمين';
include '../../includes/admin_header.php';
?>

<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['total_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المستخدمين النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['active_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الخطة المجانية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['free_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-gift fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                جديد اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['new_today']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card shadow-sm mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>
                البحث والتصفية
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="اسم المستخدم، البريد الإلكتروني، الاسم...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>نشط</option>
                        <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>موقوف</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="plan" class="form-label">خطة الاشتراك</label>
                    <select class="form-select" id="plan" name="plan">
                        <option value="">جميع الخطط</option>
                        <option value="free" <?php echo $plan === 'free' ? 'selected' : ''; ?>>مجانية</option>
                        <option value="basic" <?php echo $plan === 'basic' ? 'selected' : ''; ?>>أساسية</option>
                        <option value="premium" <?php echo $plan === 'premium' ? 'selected' : ''; ?>>مميزة</option>
                        <option value="enterprise" <?php echo $plan === 'enterprise' ? 'selected' : ''; ?>>مؤسسية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow-sm">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users me-2"></i>
                قائمة المستخدمين (<?php echo formatArabicNumber($totalUsers); ?>)
            </h6>
            <?php if (hasAdminPermission('users', 'create')): ?>
            <a href="add.php" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i>إضافة مستخدم
            </a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if (empty($users)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستخدمين</h5>
                    <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الشركة</th>
                                <th>خطة الاشتراك</th>
                                <th>الحالة</th>
                                <th>الإحصائيات</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="https://ui-avatars.com/api/?name=<?php echo urlencode($user['first_name'] . '+' . $user['last_name']); ?>&background=4e73df&color=fff&size=40" 
                                             class="rounded-circle me-3" width="40" height="40" alt="Avatar">
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                            <div class="text-muted small">@<?php echo htmlspecialchars($user['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><?php echo htmlspecialchars($user['company_name'] ?: '-'); ?></td>
                                <td>
                                    <?php
                                    $planColors = [
                                        'free' => 'secondary',
                                        'basic' => 'info',
                                        'premium' => 'warning',
                                        'enterprise' => 'success'
                                    ];
                                    $planNames = [
                                        'free' => 'مجانية',
                                        'basic' => 'أساسية',
                                        'premium' => 'مميزة',
                                        'enterprise' => 'مؤسسية'
                                    ];
                                    $planColor = $planColors[$user['subscription_plan']] ?? 'secondary';
                                    $planName = $planNames[$user['subscription_plan']] ?? $user['subscription_plan'];
                                    ?>
                                    <span class="badge bg-<?php echo $planColor; ?>"><?php echo $planName; ?></span>
                                </td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">موقوف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <div><i class="fas fa-file-invoice me-1"></i><?php echo formatArabicNumber($user['invoice_count']); ?> فاتورة</div>
                                        <div><i class="fas fa-users me-1"></i><?php echo formatArabicNumber($user['client_count']); ?> عميل</div>
                                        <div><i class="fas fa-dollar-sign me-1"></i><?php echo formatArabicNumber($user['total_revenue'] ?: 0); ?> ر.س</div>
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y/m/d', strtotime($user['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if (hasAdminPermission('users', 'view')): ?>
                                        <a href="view.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php endif; ?>
                                        
                                        <?php if (hasAdminPermission('users', 'edit')): ?>
                                        <a href="edit.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <?php if ($user['is_active']): ?>
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'suspend')" title="إيقاف">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <?php else: ?>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'activate')" title="تفعيل">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                        
                                        <?php if (hasAdminPermission('users', 'delete')): ?>
                                        <button type="button" class="btn btn-outline-danger btn-delete" 
                                                onclick="deleteUser(<?php echo $user['id']; ?>)" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <nav aria-label="صفحات المستخدمين">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                        </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId, action) {
    const actionText = action === 'suspend' ? 'إيقاف' : 'تفعيل';
    
    if (!confirm(`هل أنت متأكد من ${actionText} هذا المستخدم؟`)) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', action + '_user');
    formData.append('user_id', userId);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ غير متوقع', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

function deleteUser(userId) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟\nسيتم حذف جميع بياناته نهائياً ولا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'delete_user');
    formData.append('user_id', userId);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ غير متوقع', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}
</script>

<?php include '../../includes/admin_footer.php'; ?>
