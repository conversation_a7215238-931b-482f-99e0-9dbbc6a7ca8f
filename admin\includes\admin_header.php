<?php
/**
 * Admin Header Template
 * Multi-Tenant Administrative Management System
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Access denied');
}

$currentAdmin = getCurrentAdmin();
$pageTitle = $pageTitle ?? 'النظام الإداري';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . ADMIN_NAME; ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Admin CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
    
    <style>
        :root {
            --admin-primary: #4e73df;
            --admin-secondary: #858796;
            --admin-success: #1cc88a;
            --admin-info: #36b9cc;
            --admin-warning: #f6c23e;
            --admin-danger: #e74a3b;
            --admin-light: #f8f9fc;
            --admin-dark: #5a5c69;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--admin-light);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--admin-primary) 10%, #224abe 100%);
            min-height: 100vh;
            width: 250px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .sidebar-brand {
            padding: 1.5rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-brand h4 {
            color: white;
            margin: 0;
            font-size: 1.2rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin-bottom: 0.5rem;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        .nav-link i {
            width: 20px;
            margin-left: 0.75rem;
        }
        
        .main-content {
            margin-right: 250px;
            transition: all 0.3s;
        }
        
        .main-content.expanded {
            margin-right: 80px;
        }
        
        .topbar {
            background: white;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 0;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, var(--admin-primary) 0%, #224abe 100%);
        }
        
        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--admin-primary) 0%, #224abe 100%);
        }
        
        .card {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .btn-primary {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
        }
        
        .text-primary {
            color: var(--admin-primary) !important;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--admin-danger);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand">
            <h4>
                <i class="fas fa-shield-alt me-2"></i>
                <span class="brand-text">النظام الإداري</span>
            </h4>
        </div>
        
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" 
                   href="../dashboard/">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            
            <?php if (hasAdminPermission('users', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/users/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/users/">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('invoices', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/invoices/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/invoices/">
                    <i class="fas fa-file-invoice"></i>
                    <span>إدارة الفواتير</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('clients', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/clients/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/clients/">
                    <i class="fas fa-handshake"></i>
                    <span>إدارة العملاء</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('products', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/products/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/products/">
                    <i class="fas fa-boxes"></i>
                    <span>إدارة المنتجات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('reports', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/reports/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/reports/">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير والإحصائيات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('logs', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/audit/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/audit/">
                    <i class="fas fa-history"></i>
                    <span>سجل الأنشطة</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasAdminPermission('settings', 'view')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/system/') !== false ? 'active' : ''; ?>" 
                   href="../dashboard/system/">
                    <i class="fas fa-cogs"></i>
                    <span>إعدادات النظام</span>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="topbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0"><?php echo $pageTitle; ?></h5>
            </div>
            
            <div class="d-flex align-items-center">
                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-link text-dark position-relative" type="button" 
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-user-plus text-success me-2"></i>
                            مستخدم جديد انضم للمنصة
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-file-invoice text-info me-2"></i>
                            فاتورة جديدة تم إنشاؤها
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                            فاتورة متأخرة السداد
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
                    </ul>
                </div>
                
                <!-- User Menu -->
                <div class="dropdown">
                    <button class="btn btn-link text-dark d-flex align-items-center" type="button" 
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <img src="https://ui-avatars.com/api/?name=<?php echo urlencode($currentAdmin['first_name'] . '+' . $currentAdmin['last_name']); ?>&background=4e73df&color=fff&size=32" 
                             class="rounded-circle me-2" width="32" height="32" alt="Avatar">
                        <span><?php echo htmlspecialchars($currentAdmin['first_name']); ?></span>
                        <i class="fas fa-chevron-down ms-2"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            <?php echo htmlspecialchars($currentAdmin['first_name'] . ' ' . $currentAdmin['last_name']); ?>
                        </h6></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
