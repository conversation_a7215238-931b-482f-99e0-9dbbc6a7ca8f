<?php
/**
 * Admin Header Template
 * Common header for all admin pages
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not allowed');
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: /admin/dashboard/login.php');
    exit;
}

// Check session timeout
if (!checkAdminSessionTimeout()) {
    header('Location: /admin/dashboard/login.php?timeout=1');
    exit;
}

$currentAdmin = getCurrentAdmin();
$pageTitle = $pageTitle ?? 'لوحة التحكم الإدارية';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - <?php echo ADMIN_CONFIG['system_name']; ?></title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom Admin CSS -->
    <link href="/admin/assets/css/admin.css" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo htmlspecialchars($css); ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        :root {
            --admin-primary: #2c3e50;
            --admin-secondary: #34495e;
            --admin-success: #27ae60;
            --admin-danger: #e74c3c;
            --admin-warning: #f39c12;
            --admin-info: #3498db;
            --admin-light: #ecf0f1;
            --admin-dark: #2c3e50;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .admin-navbar {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-navbar .navbar-brand {
            color: white !important;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .admin-navbar .nav-link {
            color: rgba(255,255,255,0.9) !important;
            transition: color 0.3s ease;
        }
        
        .admin-navbar .nav-link:hover {
            color: white !important;
        }
        
        .admin-sidebar {
            background: white;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar .nav-link {
            color: var(--admin-dark);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            background-color: var(--admin-primary);
            color: white;
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }
        
        .admin-content {
            padding: 20px;
        }
        
        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }
        
        .admin-card .card-header {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }
        
        .btn-admin-primary {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border: none;
            color: white;
        }
        
        .btn-admin-primary:hover {
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-primary));
            color: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard/">
                <i class="fas fa-tachometer-alt me-2"></i>
                <?php echo ADMIN_CONFIG['system_name']; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard/">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($currentAdmin['first_name'] . ' ' . $currentAdmin['last_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="/admin/dashboard/profile.php">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/admin/dashboard/settings.php">
                                    <i class="fas fa-cog me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="/admin/dashboard/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <?php include ADMIN_INCLUDES_PATH . '/admin_sidebar.php'; ?>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content">
                    <!-- Breadcrumb -->
                    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
                        <nav aria-label="breadcrumb" class="mb-4">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/admin/dashboard/">الرئيسية</a>
                                </li>
                                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                    <?php if (isset($breadcrumb['url'])): ?>
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo htmlspecialchars($breadcrumb['url']); ?>">
                                                <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="breadcrumb-item active" aria-current="page">
                                            <?php echo htmlspecialchars($breadcrumb['title']); ?>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ol>
                        </nav>
                    <?php endif; ?>
                    
                    <!-- Page Title -->
                    <?php if (isset($pageTitle)): ?>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h1 class="h3 mb-0"><?php echo htmlspecialchars($pageTitle); ?></h1>
                            <?php if (isset($pageActions)): ?>
                                <div class="page-actions">
                                    <?php echo $pageActions; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Alert Messages -->
                    <?php if (isset($_GET['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($_GET['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php 
                            $errorMessages = [
                                'unauthorized' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
                                'invalid_request' => 'طلب غير صحيح',
                                'not_found' => 'العنصر المطلوب غير موجود',
                                'database_error' => 'خطأ في قاعدة البيانات'
                            ];
                            echo htmlspecialchars($errorMessages[$_GET['error']] ?? $_GET['error']);
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Main Content Area -->
                    <div class="main-content">
