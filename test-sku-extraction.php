<?php
require_once 'includes/SafeFileReader.php';

echo "<h1>🔍 SKU Extraction Test</h1>";

// Create a simple test CSV file
$test_csv_content = "name;description;price;quantity;category;sku;status\nTest Product;Test Description;100.00;10;Test;TEST-SKU-001;active\nAnother Product;Another Description;200.00;5;Test;TEST-SKU-002;active";

file_put_contents('temp-test-sku.csv', $test_csv_content);

echo "<h2>1. Test CSV Content:</h2>";
echo "<pre>" . htmlspecialchars($test_csv_content) . "</pre>";

echo "<h2>2. SafeFileReader Processing:</h2>";

try {
    $reader = new SafeFileReader('temp-test-sku.csv', 'temp-test-sku.csv');
    $reader->validateFile();
    $rows = $reader->readFile();
    
    echo "<div style='color: green;'>✅ File read successfully</div>";
    echo "<div>📊 Total rows: " . count($rows) . "</div>";
    
    if (count($rows) > 0) {
        echo "<h3>Headers (Row 0):</h3>";
        foreach ($rows[0] as $index => $header) {
            echo "<div>[$index] '" . htmlspecialchars($header) . "'</div>";
        }
        
        if (count($rows) > 1) {
            echo "<h3>First Product (Row 1):</h3>";
            foreach ($rows[1] as $index => $value) {
                echo "<div>[$index] '" . htmlspecialchars($value) . "'</div>";
            }
            
            echo "<h3>Second Product (Row 2):</h3>";
            if (isset($rows[2])) {
                foreach ($rows[2] as $index => $value) {
                    echo "<div>[$index] '" . htmlspecialchars($value) . "'</div>";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>3. Header Mapping Test:</h2>";

if (isset($rows) && count($rows) > 0) {
    // Clean headers
    $headers = array_map(function($header) {
        $cleaned = trim($header);
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = strtolower($cleaned);
        return $cleaned;
    }, $rows[0]);
    
    echo "<h3>Cleaned Headers:</h3>";
    foreach ($headers as $index => $header) {
        echo "<div>[$index] '" . htmlspecialchars($header) . "'</div>";
    }
    
    // Header mapping
    $header_map = [
        'name' => ['name', 'اسم المنتج', 'product name', 'اسم', 'المنتج'],
        'price' => ['price', 'السعر', 'سعر', 'cost', 'amount'],
        'quantity' => ['quantity', 'الكمية', 'كمية', 'qty', 'stock'],
        'description' => ['description', 'الوصف', 'وصف', 'desc'],
        'category' => ['category', 'الفئة', 'فئة', 'cat'],
        'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code'],
        'status' => ['status', 'الحالة', 'حالة', 'state']
    ];
    
    $found_headers = [];
    
    foreach ($header_map as $standard_name => $alternatives) {
        $found = false;
        foreach ($alternatives as $alt) {
            $alt_cleaned = strtolower(trim($alt));
            $position = array_search($alt_cleaned, $headers);
            if ($position !== false) {
                $found_headers[$standard_name] = $position;
                $found = true;
                echo "<div style='color: green;'>✅ Found '$standard_name' at position $position (matched '$alt_cleaned')</div>";
                break;
            }
        }
        if (!$found) {
            echo "<div style='color: red;'>❌ '$standard_name' not found</div>";
        }
    }
    
    echo "<h2>4. Data Extraction Test:</h2>";
    
    if (count($rows) > 1 && isset($found_headers['sku'])) {
        $sku_position = $found_headers['sku'];
        echo "<div>SKU column position: $sku_position</div>";
        
        for ($i = 1; $i < count($rows); $i++) {
            $row = $rows[$i];
            echo "<h3>Row $i:</h3>";
            
            $raw_sku = isset($row[$sku_position]) ? $row[$sku_position] : null;
            $trimmed_sku = $raw_sku !== null ? trim($raw_sku) : '';
            
            echo "<div>Raw SKU: '" . htmlspecialchars($raw_sku ?? 'NULL') . "'</div>";
            echo "<div>Trimmed SKU: '" . htmlspecialchars($trimmed_sku) . "'</div>";
            echo "<div>Is Empty: " . (empty($trimmed_sku) ? 'YES ❌' : 'NO ✅') . "</div>";
            echo "<div>Length: " . strlen($trimmed_sku) . "</div>";
            
            // Test the exact logic from import-products.php
            $cleaned_sku = trim($raw_sku ?? '');
            $final_sku = !empty($cleaned_sku) ? $cleaned_sku : '';
            $sku_source = !empty($cleaned_sku) ? 'provided' : 'missing';
            
            echo "<div>Final SKU: '" . htmlspecialchars($final_sku) . "'</div>";
            echo "<div>SKU Source: $sku_source</div>";
            
            if (empty($final_sku)) {
                echo "<div style='color: red;'>❌ Would trigger validation error</div>";
            } else {
                echo "<div style='color: green;'>✅ Would pass validation</div>";
            }
            
            echo "<hr>";
        }
    } else {
        echo "<div style='color: red;'>❌ SKU column not found or no data rows</div>";
    }
}

// Clean up
if (file_exists('temp-test-sku.csv')) {
    unlink('temp-test-sku.csv');
}

echo "<h2>5. Test with Actual Sample File:</h2>";

if (file_exists('sample-products-test.csv')) {
    echo "<h3>Testing sample-products-test.csv:</h3>";
    
    try {
        $reader = new SafeFileReader('sample-products-test.csv', 'sample-products-test.csv');
        $rows = $reader->readFile();
        
        echo "<div>Rows count: " . count($rows) . "</div>";
        
        if (count($rows) > 0) {
            echo "<h4>Headers:</h4>";
            foreach ($rows[0] as $i => $h) {
                echo "<div>[$i] '" . htmlspecialchars($h) . "'</div>";
            }
            
            // Find SKU column
            $headers = array_map('strtolower', array_map('trim', $rows[0]));
            $sku_pos = array_search('sku', $headers);
            
            if ($sku_pos !== false) {
                echo "<div style='color: green;'>✅ SKU column found at position $sku_pos</div>";
                
                if (count($rows) > 1) {
                    echo "<h4>First few SKU values:</h4>";
                    for ($i = 1; $i < min(4, count($rows)); $i++) {
                        $sku_val = isset($rows[$i][$sku_pos]) ? $rows[$i][$sku_pos] : 'NULL';
                        echo "<div>Row $i SKU: '" . htmlspecialchars($sku_val) . "' (empty: " . (empty(trim($sku_val)) ? 'YES' : 'NO') . ")</div>";
                    }
                }
            } else {
                echo "<div style='color: red;'>❌ SKU column not found</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
} else {
    echo "<div style='color: orange;'>⚠️ sample-products-test.csv not found</div>";
}
?>
