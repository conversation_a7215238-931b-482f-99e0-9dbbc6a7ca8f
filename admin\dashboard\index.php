<?php
/**
 * Admin Dashboard - Main Page
 * Multi-Tenant Administrative Management System
 */

define('ADMIN_SYSTEM', true);
require_once '../config/admin_config.php';

// Require admin login
requireAdminLogin();

// Get current admin
$currentAdmin = getCurrentAdmin();

// Get system statistics with caching
$stats = getSystemStatsCache('dashboard_stats', function() {
    $pdo = getAdminDBConnection();
    
    // Users statistics
    $userStats = $pdo->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN subscription_plan = 'free' THEN 1 END) as free_users,
            COUNT(CASE WHEN subscription_plan = 'premium' THEN 1 END) as premium_users,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
        FROM users
    ")->fetch(PDO::FETCH_ASSOC);
    
    // Invoices statistics
    $invoiceStats = $pdo->query("
        SELECT 
            COUNT(*) as total_invoices,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_invoices,
            COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN status IN ('sent', 'overdue') THEN total_amount ELSE 0 END) as pending_amount,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
        FROM invoices
    ")->fetch(PDO::FETCH_ASSOC);
    
    // Clients statistics
    $clientStats = $pdo->query("
        SELECT 
            COUNT(*) as total_clients,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_clients,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
        FROM clients
    ")->fetch(PDO::FETCH_ASSOC);
    
    // Products statistics (if table exists)
    $productStats = ['total_products' => 0, 'active_products' => 0, 'new_today' => 0];
    try {
        $productStats = $pdo->query("
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_products,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today
            FROM products
        ")->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Products table doesn't exist yet
    }
    
    // Monthly revenue trend
    $monthlyRevenue = $pdo->query("
        SELECT 
            DATE_FORMAT(issue_date, '%Y-%m') as month,
            SUM(total_amount) as revenue,
            COUNT(*) as invoice_count
        FROM invoices 
        WHERE status = 'paid' 
        AND issue_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(issue_date, '%Y-%m')
        ORDER BY month DESC
        LIMIT 12
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent activities
    $recentActivities = $pdo->query("
        SELECT 
            al.action,
            al.description,
            al.created_at,
            au.first_name,
            au.last_name,
            au.username
        FROM admin_logs al
        LEFT JOIN admin_users au ON al.admin_id = au.id
        ORDER BY al.created_at DESC
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'users' => $userStats,
        'invoices' => $invoiceStats,
        'clients' => $clientStats,
        'products' => $productStats,
        'monthly_revenue' => array_reverse($monthlyRevenue),
        'recent_activities' => $recentActivities
    ];
}, 300); // Cache for 5 minutes

$pageTitle = 'لوحة التحكم الرئيسية';
include '../includes/admin_header.php';
?>

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card p-4 bg-gradient-primary text-white rounded-3">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">مرحباً، <?php echo htmlspecialchars($currentAdmin['first_name']); ?>!</h2>
                        <p class="mb-0 opacity-75">نظرة شاملة على منصة الفواتير متعددة المستأجرين</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="text-white-50">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <?php echo date('Y/m/d - H:i'); ?>
                        </div>
                        <div class="text-white-50 mt-1">
                            <i class="fas fa-user-shield me-2"></i>
                            <?php echo ucfirst($currentAdmin['role']); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Users Statistics -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['users']['total_users']); ?>
                            </div>
                            <div class="text-xs text-success mt-1">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo $stats['users']['new_today']; ?> جديد اليوم
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoices Statistics -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي الفواتير
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['invoices']['total_invoices']); ?>
                            </div>
                            <div class="text-xs text-success mt-1">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo $stats['invoices']['new_today']; ?> جديد اليوم
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Statistics -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي الإيرادات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['invoices']['total_revenue']); ?> ر.س
                            </div>
                            <div class="text-xs text-warning mt-1">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo formatArabicNumber($stats['invoices']['pending_amount']); ?> ر.س معلق
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clients Statistics -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                إجمالي العملاء
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo formatArabicNumber($stats['clients']['total_clients']); ?>
                            </div>
                            <div class="text-xs text-success mt-1">
                                <i class="fas fa-plus me-1"></i>
                                <?php echo $stats['clients']['new_today']; ?> جديد اليوم
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-handshake fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Recent Activities -->
    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>
                        اتجاه الإيرادات الشهرية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        الأنشطة الأخيرة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="activity-list" style="max-height: 400px; overflow-y: auto;">
                        <?php if (empty($stats['recent_activities'])): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>لا توجد أنشطة حديثة</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($stats['recent_activities'] as $activity): ?>
                                <div class="activity-item d-flex align-items-start mb-3">
                                    <div class="activity-icon me-3">
                                        <i class="fas fa-circle text-primary" style="font-size: 0.5rem;"></i>
                                    </div>
                                    <div class="activity-content flex-grow-1">
                                        <div class="activity-description">
                                            <?php echo htmlspecialchars($activity['description']); ?>
                                        </div>
                                        <div class="activity-meta text-muted small">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?>
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('Y/m/d H:i', strtotime($activity['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php if (hasAdminPermission('users', 'view')): ?>
                        <div class="col-md-3 mb-3">
                            <a href="users/" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <div>إدارة المستخدمين</div>
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (hasAdminPermission('invoices', 'view')): ?>
                        <div class="col-md-3 mb-3">
                            <a href="invoices/" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                <div>إدارة الفواتير</div>
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (hasAdminPermission('reports', 'view')): ?>
                        <div class="col-md-3 mb-3">
                            <a href="reports/" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <div>التقارير والإحصائيات</div>
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (hasAdminPermission('settings', 'view')): ?>
                        <div class="col-md-3 mb-3">
                            <a href="system/" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-cogs fa-2x mb-2"></i>
                                <div>إعدادات النظام</div>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueData = <?php echo json_encode($stats['monthly_revenue']); ?>;
const ctx = document.getElementById('revenueChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: revenueData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' });
        }),
        datasets: [{
            label: 'الإيرادات (ر.س)',
            data: revenueData.map(item => item.revenue),
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('ar-SA').format(value) + ' ر.س';
                    }
                }
            }
        }
    }
});
</script>

<?php include '../includes/admin_footer.php'; ?>
