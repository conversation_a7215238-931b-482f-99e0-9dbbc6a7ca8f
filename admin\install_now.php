<?php
/**
 * Simple Admin System Installation
 * Run this script to install the admin system
 */

// Include the main database configuration
require_once __DIR__ . '/../config/database.php';

echo "Starting Admin System Installation...\n";

try {
    // Create Database instance
    $db = new Database();
    $pdo = $db->connect();
    
    echo "Database connection established.\n";
    
    // Read and execute the admin schema
    $schemaFile = __DIR__ . '/database/admin_schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    if ($schema === false) {
        throw new Exception("Could not read schema file");
    }
    
    echo "Executing database schema...\n";
    
    // Split the schema into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                // Ignore table already exists errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
                echo "⚠ Skipped (already exists): " . substr($statement, 0, 50) . "...\n";
            }
        }
    }
    
    echo "\n✅ Database schema installed successfully!\n";
    
    // Create admin directories
    $directories = [
        'admin/api',
        'admin/api/v1',
        'admin/api/v1/auth',
        'admin/api/v1/users',
        'admin/api/v1/invoices',
        'admin/api/v1/reports',
        'admin/dashboard/users',
        'admin/dashboard/invoices',
        'admin/dashboard/clients',
        'admin/dashboard/products',
        'admin/dashboard/reports',
        'admin/dashboard/system',
        'admin/dashboard/audit',
        'admin/assets',
        'admin/assets/css',
        'admin/assets/js',
        'admin/assets/images',
        'admin/logs',
        'admin/uploads'
    ];
    
    echo "\nCreating directory structure...\n";
    foreach ($directories as $dir) {
        $fullPath = __DIR__ . '/../' . $dir;
        if (!is_dir($fullPath)) {
            if (mkdir($fullPath, 0755, true)) {
                echo "✓ Created: $dir\n";
            } else {
                echo "⚠ Failed to create: $dir\n";
            }
        } else {
            echo "⚠ Already exists: $dir\n";
        }
    }
    
    // Create .htaccess files for security
    $htaccessContent = "Order Deny,Allow\nDeny from all\n";
    
    $protectedDirs = ['admin/config', 'admin/logs', 'admin/database'];
    foreach ($protectedDirs as $dir) {
        $htaccessFile = __DIR__ . '/../' . $dir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, $htaccessContent);
            echo "✓ Created security file: $dir/.htaccess\n";
        }
    }
    
    // Create installation marker
    $markerFile = __DIR__ . '/admin_installed.txt';
    file_put_contents($markerFile, date('Y-m-d H:i:s') . " - Admin system installed successfully\n");
    
    echo "\n🎉 Admin System Installation Complete!\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "✅ Database schema installed\n";
    echo "✅ Directory structure created\n";
    echo "✅ Security files configured\n";
    echo "✅ Default admin user created\n";
    echo "\n📋 Login Details:\n";
    echo "URL: http://localhost/invoice/admin/dashboard/\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "Email: <EMAIL>\n";
    echo "\n⚠️  IMPORTANT: Change the default password after first login!\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
} catch (Exception $e) {
    echo "\n❌ Installation failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
