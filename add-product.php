<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

$success = '';
$error = '';
$form_data = [];

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();
        
        // جمع البيانات من النموذج
        $form_data = [
            'name' => sanitize($_POST['name'] ?? ''),
            'description' => sanitize($_POST['description'] ?? ''),
            'price' => (float)($_POST['price'] ?? 0),
            'quantity' => (int)($_POST['quantity'] ?? 0),
            'category' => sanitize($_POST['category'] ?? ''),
            'sku' => sanitize($_POST['sku'] ?? ''),
            'status' => $_POST['status'] ?? 'active'
        ];
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($form_data['name'])) {
            $errors[] = 'اسم المنتج مطلوب';
        }

        if ($form_data['price'] < 0) {
            $errors[] = 'السعر يجب أن يكون أكبر من أو يساوي صفر';
        }

        if ($form_data['quantity'] < 0) {
            $errors[] = 'الكمية يجب أن تكون أكبر من أو يساوي صفر';
        }

        // التحقق من وجود رمز المنتج (إجباري)
        if (empty($form_data['sku'])) {
            $errors[] = 'رمز المنتج (SKU) مطلوب ولا يمكن تركه فارغاً';
        } else {
            // التحقق من صحة تنسيق SKU (أحرف وأرقام وشرطات فقط)
            if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $form_data['sku'])) {
                $errors[] = 'رمز المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط';
            }

            // التحقق من عدم تكرار رمز المنتج
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ? AND sku = ?");
            $stmt->execute([$user_id, $form_data['sku']]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = 'رمز المنتج موجود بالفعل';
            }
        }
        
        if (!in_array($form_data['status'], ['active', 'inactive'])) {
            $form_data['status'] = 'active';
        }
        
        if (!empty($errors)) {
            $error = implode('<br>', $errors);
        } else {
            // إدراج المنتج الجديد
            $stmt = $pdo->prepare("
                INSERT INTO products (user_id, name, description, price, quantity, category, sku, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $user_id,
                $form_data['name'],
                $form_data['description'],
                $form_data['price'],
                $form_data['quantity'],
                $form_data['category'],
                $form_data['sku'],
                $form_data['status']
            ]);
            
            $success = 'تم إضافة المنتج بنجاح';
            
            // إعادة توجيه إلى صفحة المنتجات بعد 2 ثانية
            header("refresh:2;url=products.php");
            
            // مسح البيانات بعد النجاح
            $form_data = [];
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الفئات الموجودة للاقتراح
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT DISTINCT category FROM products WHERE user_id = ? AND category IS NOT NULL ORDER BY category");
    $stmt->execute([$user_id]);
    $existing_categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $existing_categories = [];
}

$page_title = 'إضافة منتج جديد';
include 'includes/header.php';
?>

<div class="container py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <div class="mt-2">
                <small>سيتم توجيهك إلى صفحة المنتجات خلال ثوانٍ...</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-plus-circle text-primary me-2"></i>
                إضافة منتج جديد
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="products.php">المنتجات</a></li>
                    <li class="breadcrumb-item active">إضافة منتج جديد</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة إلى المنتجات
            </a>
        </div>
    </div>

    <!-- نموذج إضافة المنتج -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>بيانات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="productForm">
                        <div class="row">
                            <!-- اسم المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($form_data['name'] ?? ''); ?>" 
                                       required maxlength="255">
                                <div class="form-text">أدخل اسم المنتج بوضوح</div>
                            </div>
                            
                            <!-- رمز المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="sku" class="form-label">رمز المنتج (SKU) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="sku" name="sku"
                                       value="<?php echo htmlspecialchars($form_data['sku'] ?? ''); ?>"
                                       required maxlength="100" pattern="[a-zA-Z0-9\-_]+">
                                <div class="form-text">رمز فريد للمنتج (أحرف وأرقام وشرطات فقط)</div>
                            </div>
                        </div>
                        
                        <!-- وصف المنتج -->
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="أدخل وصف تفصيلي للمنتج"><?php echo htmlspecialchars($form_data['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <!-- السعر -->
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">السعر (ر.س) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="<?php echo $form_data['price'] ?? ''; ?>" 
                                           step="0.01" min="0" required>
                                    <span class="input-group-text">ر.س</span>
                                </div>
                            </div>
                            
                            <!-- الكمية -->
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">الكمية المتاحة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       value="<?php echo $form_data['quantity'] ?? ''; ?>" 
                                       min="0" required>
                            </div>
                            
                            <!-- الحالة -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">حالة المنتج</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($form_data['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($form_data['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- الفئة -->
                        <div class="mb-4">
                            <label for="category" class="form-label">فئة المنتج</label>
                            <input type="text" class="form-control" id="category" name="category" 
                                   value="<?php echo htmlspecialchars($form_data['category'] ?? ''); ?>" 
                                   list="categories" maxlength="100">
                            <datalist id="categories">
                                <?php foreach ($existing_categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                <?php endforeach; ?>
                            </datalist>
                            <div class="form-text">اختر فئة موجودة أو أدخل فئة جديدة</div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ المنتج
                                </button>
                                <button type="reset" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                            </div>
                            <div>
                                <a href="products.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>نصائح مفيدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح لإضافة المنتجات:</h6>
                        <ul class="mb-0 small">
                            <li>استخدم أسماء واضحة ووصفية للمنتجات</li>
                            <li>أضف رمز المنتج (SKU) لتسهيل التتبع</li>
                            <li>اكتب وصف مفصل يساعد العملاء</li>
                            <li>حدد السعر والكمية بدقة</li>
                            <li>اختر الفئة المناسبة للتنظيم</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                        <p class="mb-0 small">تأكد من صحة البيانات قبل الحفظ. يمكنك تعديل المنتج لاحقاً من صفحة المنتجات.</p>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ?");
                        $stmt->execute([$user_id]);
                        $total_products = $stmt->fetchColumn();
                        
                        $stmt = $pdo->prepare("SELECT COUNT(DISTINCT category) FROM products WHERE user_id = ? AND category IS NOT NULL");
                        $stmt->execute([$user_id]);
                        $total_categories = $stmt->fetchColumn();
                    } catch (Exception $e) {
                        $total_products = 0;
                        $total_categories = 0;
                    }
                    ?>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-0"><?php echo number_format($total_products); ?></h4>
                                <small class="text-muted">إجمالي المنتجات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-0"><?php echo number_format($total_categories); ?></h4>
                            <small class="text-muted">عدد الفئات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج قبل الإرسال
document.getElementById('productForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const sku = document.getElementById('sku').value.trim();
    const price = parseFloat(document.getElementById('price').value);
    const quantity = parseInt(document.getElementById('quantity').value);

    if (!name) {
        alert('يرجى إدخال اسم المنتج');
        e.preventDefault();
        return;
    }

    if (!sku) {
        alert('يرجى إدخال رمز المنتج (SKU) - هذا الحقل إجباري');
        e.preventDefault();
        return;
    }

    // التحقق من صحة تنسيق SKU
    const skuPattern = /^[a-zA-Z0-9\-_]+$/;
    if (!skuPattern.test(sku)) {
        alert('رمز المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط');
        e.preventDefault();
        return;
    }

    if (price < 0) {
        alert('السعر يجب أن يكون أكبر من أو يساوي صفر');
        e.preventDefault();
        return;
    }

    if (quantity < 0) {
        alert('الكمية يجب أن تكون أكبر من أو يساوي صفر');
        e.preventDefault();
        return;
    }
});

// إضافة تنبيه للمستخدم حول أهمية SKU
document.getElementById('sku').addEventListener('focus', function() {
    if (!this.value) {
        this.placeholder = 'مثال: PROD-001 أو LAPTOP-2024';
    }
});

// التحقق الفوري من صحة تنسيق SKU
document.getElementById('sku').addEventListener('input', function() {
    const sku = this.value.trim();
    const skuPattern = /^[a-zA-Z0-9\-_]*$/;

    if (sku && !skuPattern.test(sku)) {
        this.setCustomValidity('رمز المنتج يجب أن يحتوي على أحرف وأرقام وشرطات فقط');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
