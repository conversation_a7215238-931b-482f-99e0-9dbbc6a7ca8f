<?php
require_once 'includes/SafeFileReader.php';

echo "<h1>🔍 Comprehensive SKU Import Debugging</h1>";

// Test files to check
$test_files = [
    'sample-products-test.csv',
    'test-valid-sku.csv',
    'test-missing-sku.csv'
];

foreach ($test_files as $test_file) {
    if (!file_exists($test_file)) {
        echo "<div style='color: red; margin: 10px 0;'>❌ File not found: $test_file</div>";
        continue;
    }
    
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px; border-radius: 8px;'>";
    echo "<h2>📁 Testing File: $test_file</h2>";
    
    // Step 1: Raw file content
    echo "<h3>Step 1: Raw File Content</h3>";
    $raw_content = file_get_contents($test_file);
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars($raw_content);
    echo "</div>";
    
    // Step 2: File reading with SafeFileReader
    echo "<h3>Step 2: SafeFileReader Processing</h3>";
    try {
        $reader = new SafeFileReader($test_file, $test_file);
        $reader->validateFile();
        $rows = $reader->readFile();
        
        echo "<div style='color: green;'>✅ File read successfully</div>";
        echo "<div>📊 Total rows: " . count($rows) . "</div>";
        
        if (count($rows) > 0) {
            echo "<h4>Headers (Row 0):</h4>";
            echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 4px;'>";
            foreach ($rows[0] as $index => $header) {
                echo "<div>[$index] '" . htmlspecialchars($header) . "' (length: " . strlen($header) . ")</div>";
            }
            echo "</div>";
            
            if (count($rows) > 1) {
                echo "<h4>First Product (Row 1):</h4>";
                echo "<div style='background: #f3e5f5; padding: 10px; border-radius: 4px;'>";
                foreach ($rows[1] as $index => $value) {
                    echo "<div>[$index] '" . htmlspecialchars($value) . "' (length: " . strlen($value) . ")</div>";
                }
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Error reading file: " . htmlspecialchars($e->getMessage()) . "</div>";
        continue;
    }
    
    // Step 3: Header processing (mimicking import-products.php logic)
    echo "<h3>Step 3: Header Processing & Mapping</h3>";
    
    $headers = array_map(function($header) {
        $cleaned = trim($header);
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = strtolower($cleaned);
        return $cleaned;
    }, $rows[0]);
    
    echo "<h4>Cleaned Headers:</h4>";
    echo "<div style='background: #fff3e0; padding: 10px; border-radius: 4px;'>";
    foreach ($headers as $index => $header) {
        echo "<div>[$index] '" . htmlspecialchars($header) . "'</div>";
    }
    echo "</div>";
    
    // Header mapping logic from import-products.php
    $header_map = [
        'name' => ['name', 'اسم المنتج', 'product name', 'اسم', 'المنتج'],
        'price' => ['price', 'السعر', 'سعر', 'cost', 'amount'],
        'quantity' => ['quantity', 'الكمية', 'كمية', 'qty', 'stock'],
        'description' => ['description', 'الوصف', 'وصف', 'desc'],
        'category' => ['category', 'الفئة', 'فئة', 'cat'],
        'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code'],
        'status' => ['status', 'الحالة', 'حالة', 'state']
    ];
    
    $found_headers = [];
    $header_positions = [];
    
    foreach ($header_map as $standard_name => $alternatives) {
        $found = false;
        foreach ($alternatives as $alt) {
            $alt_cleaned = strtolower(trim($alt));
            foreach ($headers as $position => $header) {
                if ($header === $alt_cleaned) {
                    $found_headers[$standard_name] = $position;
                    $header_positions[$position] = $standard_name;
                    $found = true;
                    break 2;
                }
            }
        }
    }
    
    echo "<h4>Header Mapping Results:</h4>";
    echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 4px;'>";
    foreach ($found_headers as $standard => $position) {
        echo "<div>✅ $standard => position $position ('" . htmlspecialchars($rows[0][$position]) . "')</div>";
    }
    echo "</div>";
    
    // Check required headers
    $required_headers = ['name', 'price', 'quantity', 'sku'];
    $missing_headers = [];
    foreach ($required_headers as $header) {
        if (!isset($found_headers[$header])) {
            $missing_headers[] = $header;
        }
    }
    
    if (!empty($missing_headers)) {
        echo "<div style='color: red; background: #ffebee; padding: 10px; border-radius: 4px;'>";
        echo "❌ Missing required headers: " . implode(', ', $missing_headers);
        echo "</div>";
        continue;
    } else {
        echo "<div style='color: green; background: #e8f5e8; padding: 10px; border-radius: 4px;'>";
        echo "✅ All required headers found";
        echo "</div>";
    }
    
    // Step 4: Product data processing
    echo "<h3>Step 4: Product Data Processing</h3>";
    
    if (count($rows) > 1) {
        for ($i = 1; $i < min(4, count($rows)); $i++) { // Test first 3 products
            $row = $rows[$i];
            echo "<h4>Processing Product Row $i:</h4>";
            
            if (empty(array_filter($row))) {
                echo "<div style='color: orange;'>⚠️ Empty row, skipping</div>";
                continue;
            }
            
            $product = [];
            $row_errors = [];
            
            // Process each field
            foreach ($found_headers as $standard_name => $position) {
                $value = isset($row[$position]) ? trim($row[$position]) : '';
                
                echo "<div style='margin: 5px 0; padding: 5px; background: #f5f5f5; border-radius: 3px;'>";
                echo "<strong>$standard_name</strong> (pos $position): ";
                echo "Raw='" . htmlspecialchars($row[$position] ?? '') . "' ";
                echo "Trimmed='" . htmlspecialchars($value) . "' ";
                echo "Empty=" . (empty($value) ? 'YES' : 'NO');
                
                switch ($standard_name) {
                    case 'sku':
                        $cleaned_sku = trim($value);
                        $product['sku'] = !empty($cleaned_sku) ? $cleaned_sku : '';
                        $product['sku_source'] = !empty($cleaned_sku) ? 'provided' : 'missing';
                        
                        echo " | SKU='" . htmlspecialchars($product['sku']) . "'";
                        echo " | Source=" . $product['sku_source'];
                        
                        if (empty($product['sku'])) {
                            $row_errors[] = 'رمز المنتج (SKU) مطلوب ولا يمكن تركه فارغاً';
                            echo " | <span style='color: red;'>❌ VALIDATION FAILED</span>";
                        } else {
                            echo " | <span style='color: green;'>✅ VALIDATION PASSED</span>";
                        }
                        break;
                        
                    case 'name':
                        if (empty($value)) {
                            $row_errors[] = 'اسم المنتج مطلوب';
                        }
                        $product['name'] = $value;
                        break;
                        
                    case 'price':
                        if (empty($value)) {
                            $row_errors[] = 'السعر مطلوب';
                            $product['price'] = 0;
                        } else {
                            $price = (float)str_replace(',', '', $value);
                            if ($price < 0) {
                                $row_errors[] = 'السعر يجب أن يكون أكبر من أو يساوي صفر';
                            }
                            $product['price'] = $price;
                        }
                        break;
                        
                    case 'quantity':
                        if (empty($value)) {
                            $row_errors[] = 'الكمية مطلوبة';
                            $product['quantity'] = 0;
                        } else {
                            $quantity = (int)$value;
                            if ($quantity < 0) {
                                $row_errors[] = 'الكمية يجب أن تكون أكبر من أو تساوي صفر';
                            }
                            $product['quantity'] = $quantity;
                        }
                        break;
                        
                    default:
                        $product[$standard_name] = $value;
                        break;
                }
                echo "</div>";
            }
            
            // Show validation results
            echo "<div style='margin: 10px 0; padding: 10px; border-radius: 4px; " . 
                 (empty($row_errors) ? "background: #e8f5e8; color: green;" : "background: #ffebee; color: red;") . "'>";
            if (empty($row_errors)) {
                echo "✅ Product validation PASSED";
            } else {
                echo "❌ Product validation FAILED:<br>";
                foreach ($row_errors as $error) {
                    echo "• " . htmlspecialchars($error) . "<br>";
                }
            }
            echo "</div>";
            
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;'>";
            echo "<strong>Final Product Data:</strong><br>";
            echo "<pre>" . htmlspecialchars(print_r($product, true)) . "</pre>";
            echo "</div>";
        }
    }
    
    echo "</div>"; // End file test container
}

echo "<h2>🎯 Summary & Recommendations</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Common Issues & Solutions:</h3>";
echo "<ul>";
echo "<li><strong>Empty SKU values:</strong> Ensure all products have non-empty SKU values in your CSV/Excel file</li>";
echo "<li><strong>Wrong delimiter:</strong> Use semicolon (;) as delimiter for CSV files</li>";
echo "<li><strong>Header mismatch:</strong> Ensure column headers match expected values (name, price, quantity, sku, etc.)</li>";
echo "<li><strong>Encoding issues:</strong> Save CSV files with UTF-8 encoding</li>";
echo "<li><strong>Extra spaces:</strong> Remove leading/trailing spaces from SKU values</li>";
echo "</ul>";
echo "</div>";
?>
