<?php
// Direct test of import logic without file upload
require_once 'includes/SafeFileReader.php';

echo "<h1>🧪 Direct Import Logic Test</h1>";

// Simulate the exact data structure that would come from a CSV file
$test_data = [
    ['name', 'description', 'price', 'quantity', 'category', 'sku', 'status'],
    ['Test Product', 'A test product', '100.00', '10', 'Test', 'TEST-001', 'active'],
    ['Another Product', 'Another test', '200.00', '5', 'Test', 'TEST-002', 'active']
];

echo "<h2>Test Data:</h2>";
echo "<pre>" . htmlspecialchars(print_r($test_data, true)) . "</pre>";

// Simulate the header processing logic from import-products.php
$rows = $test_data;

$headers = array_map(function($header) {
    $cleaned = trim($header);
    $cleaned = preg_replace('/\s+/', ' ', $cleaned);
    $cleaned = strtolower($cleaned);
    return $cleaned;
}, $rows[0]);

echo "<h2>Processed Headers:</h2>";
echo "<pre>" . htmlspecialchars(print_r($headers, true)) . "</pre>";

// Header mapping
$header_map = [
    'name' => ['name', 'اسم المنتج', 'product name', 'اسم', 'المنتج'],
    'price' => ['price', 'السعر', 'سعر', 'cost', 'amount'],
    'quantity' => ['quantity', 'الكمية', 'كمية', 'qty', 'stock'],
    'description' => ['description', 'الوصف', 'وصف', 'desc'],
    'category' => ['category', 'الفئة', 'فئة', 'cat'],
    'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code'],
    'status' => ['status', 'الحالة', 'حالة', 'state']
];

$found_headers = [];
$header_positions = [];

foreach ($header_map as $standard_name => $alternatives) {
    $found = false;
    foreach ($alternatives as $alt) {
        $alt_cleaned = strtolower(trim($alt));
        foreach ($headers as $position => $header) {
            if ($header === $alt_cleaned) {
                $found_headers[$standard_name] = $position;
                $header_positions[$position] = $standard_name;
                $found = true;
                break 2;
            }
        }
    }
}

echo "<h2>Found Headers:</h2>";
echo "<pre>" . htmlspecialchars(print_r($found_headers, true)) . "</pre>";

// Process products
echo "<h2>Product Processing:</h2>";

for ($i = 1; $i < count($rows); $i++) {
    $row = $rows[$i];
    echo "<h3>Processing Row $i:</h3>";
    
    $product = [];
    $row_errors = [];
    
    foreach ($found_headers as $standard_name => $position) {
        $value = isset($row[$position]) ? trim($row[$position]) : '';
        
        switch ($standard_name) {
            case 'sku':
                $cleaned_sku = trim($value);
                $product['sku'] = !empty($cleaned_sku) ? $cleaned_sku : '';
                $product['sku_source'] = !empty($cleaned_sku) ? 'provided' : 'missing';
                
                echo "<div>SKU Processing:</div>";
                echo "<div>- Raw value: '" . htmlspecialchars($value) . "'</div>";
                echo "<div>- Cleaned: '" . htmlspecialchars($cleaned_sku) . "'</div>";
                echo "<div>- Final SKU: '" . htmlspecialchars($product['sku']) . "'</div>";
                echo "<div>- Source: " . $product['sku_source'] . "</div>";
                echo "<div>- Empty check: " . (empty($product['sku']) ? 'TRUE (empty)' : 'FALSE (not empty)') . "</div>";
                break;
                
            default:
                $product[$standard_name] = $value;
                break;
        }
    }
    
    // SKU validation (exact logic from import-products.php)
    if (empty($product['sku'])) {
        $row_errors[] = "رمز المنتج (SKU) مطلوب ولا يمكن تركه فارغاً";
        $product['sku_source'] = 'missing';
        echo "<div style='color: red;'>❌ SKU Validation FAILED: Empty SKU</div>";
    } else {
        $current_sku = trim($product['sku']);
        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $current_sku)) {
            $row_errors[] = "رمز المنتج '$current_sku' يحتوي على أحرف غير صالحة";
            echo "<div style='color: red;'>❌ SKU Validation FAILED: Invalid format</div>";
        } else {
            echo "<div style='color: green;'>✅ SKU Validation PASSED</div>";
        }
    }
    
    echo "<h4>Final Product Data:</h4>";
    echo "<pre>" . htmlspecialchars(print_r($product, true)) . "</pre>";
    
    echo "<h4>Validation Errors:</h4>";
    if (empty($row_errors)) {
        echo "<div style='color: green;'>✅ No validation errors</div>";
    } else {
        echo "<div style='color: red;'>";
        foreach ($row_errors as $error) {
            echo "❌ " . htmlspecialchars($error) . "<br>";
        }
        echo "</div>";
    }
    
    echo "<hr>";
}
?>
