<?php
require_once 'includes/SafeFileReader.php';

echo "<h1>🔍 SafeFileReader Debug Test</h1>";

// Create a simple test file
$test_content = "name;description;price;quantity;category;sku;status\nTest Product;Test Description;100.00;10;Test;TEST-SKU-001;active";

file_put_contents('debug-test.csv', $test_content);

echo "<h2>Test File Content:</h2>";
echo "<pre>" . htmlspecialchars($test_content) . "</pre>";

echo "<h2>SafeFileReader Processing:</h2>";

try {
    $reader = new SafeFileReader('debug-test.csv', 'debug-test.csv');
    $rows = $reader->readFile();
    
    echo "<div style='color: green;'>✅ File processed successfully</div>";
    echo "<div>Total rows: " . count($rows) . "</div>";
    
    foreach ($rows as $i => $row) {
        echo "<h3>Row $i:</h3>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;'>";
        foreach ($row as $j => $cell) {
            echo "<div>[$j] '" . htmlspecialchars($cell) . "' (length: " . strlen($cell) . ")</div>";
        }
        echo "</div>";
    }
    
    // Test SKU extraction specifically
    if (count($rows) > 1) {
        echo "<h2>SKU Extraction Test:</h2>";
        
        // Find SKU column
        $headers = array_map('strtolower', array_map('trim', $rows[0]));
        $sku_position = array_search('sku', $headers);
        
        if ($sku_position !== false) {
            echo "<div style='color: green;'>✅ SKU column found at position $sku_position</div>";
            
            $sku_value = isset($rows[1][$sku_position]) ? $rows[1][$sku_position] : null;
            echo "<div>Raw SKU value: '" . htmlspecialchars($sku_value ?? 'NULL') . "'</div>";
            echo "<div>Trimmed SKU: '" . htmlspecialchars(trim($sku_value ?? '')) . "'</div>";
            echo "<div>Is empty: " . (empty(trim($sku_value ?? '')) ? 'YES ❌' : 'NO ✅') . "</div>";
        } else {
            echo "<div style='color: red;'>❌ SKU column not found</div>";
            echo "<div>Available headers: " . implode(', ', $headers) . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Clean up
if (file_exists('debug-test.csv')) {
    unlink('debug-test.csv');
}

echo "<h2>Manual CSV Parsing Test:</h2>";

// Test manual parsing
$lines = explode("\n", $test_content);
echo "<h3>Lines:</h3>";
foreach ($lines as $i => $line) {
    echo "<div>Line $i: '" . htmlspecialchars($line) . "'</div>";
}

echo "<h3>Manual semicolon split:</h3>";
if (count($lines) > 1) {
    $header_parts = explode(';', $lines[0]);
    $data_parts = explode(';', $lines[1]);
    
    echo "<h4>Headers:</h4>";
    foreach ($header_parts as $i => $part) {
        echo "<div>[$i] '" . htmlspecialchars($part) . "'</div>";
    }
    
    echo "<h4>Data:</h4>";
    foreach ($data_parts as $i => $part) {
        echo "<div>[$i] '" . htmlspecialchars($part) . "'</div>";
    }
    
    // Find SKU
    $sku_index = array_search('sku', array_map('strtolower', $header_parts));
    if ($sku_index !== false && isset($data_parts[$sku_index])) {
        echo "<h4>SKU Value:</h4>";
        echo "<div style='color: green;'>✅ SKU found: '" . htmlspecialchars($data_parts[$sku_index]) . "'</div>";
    }
}
?>
