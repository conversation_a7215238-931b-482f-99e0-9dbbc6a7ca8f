<?php
/**
 * Admin System Functions
 * Core functions for the administrative management system
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not allowed');
}

/**
 * Authentication Functions
 */

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Get current admin user data
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    return $_SESSION['admin_user'] ?? null;
}

/**
 * Check admin session timeout
 */
function checkAdminSessionTimeout() {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    $lastActivity = $_SESSION['admin_last_activity'] ?? 0;
    $timeout = ADMIN_CONFIG['session_timeout'];
    
    if (time() - $lastActivity > $timeout) {
        logoutAdmin();
        return false;
    }
    
    $_SESSION['admin_last_activity'] = time();
    return true;
}

/**
 * Login admin user
 */
function loginAdmin($username, $password) {
    $pdo = getAdminDBConnection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات'];
    }
    
    try {
        // Check login attempts
        if (isAdminLocked($username)) {
            return ['success' => false, 'message' => 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول خاطئة'];
        }
        
        $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ? AND is_active = 1");
        $stmt->execute([$username]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin || !password_verify($password, $admin['password'])) {
            recordFailedLoginAttempt($username);
            return ['success' => false, 'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'];
        }
        
        // Clear failed attempts
        clearFailedLoginAttempts($username);
        
        // Update last login
        $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$admin['id']]);
        
        // Set session data
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_user'] = $admin;
        $_SESSION['admin_last_activity'] = time();
        
        // Log successful login
        logAdminActivity($admin['id'], 'login', 'admin_users', $admin['id'], null, null, 'تسجيل دخول ناجح');
        
        return ['success' => true, 'message' => 'تم تسجيل الدخول بنجاح'];
        
    } catch (Exception $e) {
        logAdminError("Login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ في النظام'];
    }
}

/**
 * Logout admin user
 */
function logoutAdmin() {
    $admin = getCurrentAdmin();
    if ($admin) {
        logAdminActivity($admin['id'], 'logout', 'admin_users', $admin['id'], null, null, 'تسجيل خروج');
    }
    
    session_unset();
    session_destroy();
}

/**
 * Check if admin is locked due to failed login attempts
 */
function isAdminLocked($username) {
    $pdo = getAdminDBConnection();
    if (!$pdo) return false;
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as attempts 
        FROM admin_activity_logs 
        WHERE action = 'failed_login' 
        AND description LIKE ? 
        AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
    ");
    $stmt->execute(["%{$username}%", ADMIN_CONFIG['lockout_duration']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $result['attempts'] >= ADMIN_CONFIG['max_login_attempts'];
}

/**
 * Record failed login attempt
 */
function recordFailedLoginAttempt($username) {
    logAdminActivity(null, 'failed_login', 'admin_users', null, null, null, "محاولة تسجيل دخول فاشلة للمستخدم: {$username}");
}

/**
 * Clear failed login attempts
 */
function clearFailedLoginAttempts($username) {
    $pdo = getAdminDBConnection();
    if (!$pdo) return;
    
    $stmt = $pdo->prepare("
        DELETE FROM admin_activity_logs 
        WHERE action = 'failed_login' 
        AND description LIKE ?
    ");
    $stmt->execute(["%{$username}%"]);
}

/**
 * Permission Functions
 */

/**
 * Check if current admin has permission
 */
function hasAdminPermission($category, $action) {
    $admin = getCurrentAdmin();
    if (!$admin) return false;
    
    // Super admin has all permissions
    if ($admin['role'] === 'super_admin') {
        return true;
    }
    
    // Check custom permissions
    if (!empty($admin['permissions'])) {
        $permissions = json_decode($admin['permissions'], true);
        return in_array($action, $permissions[$category] ?? []);
    }
    
    // Check default role permissions
    return hasPermission($admin['role'], $category, $action);
}

/**
 * Require admin permission (redirect if not authorized)
 */
function requireAdminPermission($category, $action) {
    if (!hasAdminPermission($category, $action)) {
        header('Location: /admin/dashboard/?error=unauthorized');
        exit;
    }
}

/**
 * Utility Functions
 */

/**
 * Format date for admin interface
 */
function formatAdminDate($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '-';
    
    $dateObj = new DateTime($date);
    return $dateObj->format($format);
}

/**
 * Format currency for admin interface
 */
function formatAdminCurrency($amount, $currency = 'SAR') {
    if (is_null($amount)) return '-';
    
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * Sanitize input for admin forms
 */
function sanitizeAdminInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeAdminInput', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Logging Functions
 */

/**
 * Log admin activity
 */
function logAdminActivity($adminId, $action, $entityType = null, $entityId = null, $oldValues = null, $newValues = null, $description = null) {
    if (!ADMIN_CONFIG['enable_audit_log']) return;
    
    $pdo = getAdminDBConnection();
    if (!$pdo) return;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_logs 
            (admin_user_id, action, entity_type, entity_id, old_values, new_values, description, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $adminId,
            $action,
            $entityType,
            $entityId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Log admin error
 */
function logAdminError($message, $context = []) {
    $logFile = ADMIN_LOGS_PATH . '/admin_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context) : '';
    
    $logMessage = "[{$timestamp}] ERROR: {$message} {$contextStr}" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Statistics Functions
 */

/**
 * Get dashboard statistics
 */
function getDashboardStats() {
    $pdo = getAdminDBConnection();
    if (!$pdo) return [];

    try {
        $stats = [];

        // Total users
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Active users (logged in last 30 days)
        $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE last_login > DATE_SUB(NOW(), INTERVAL 30 DAY)");
        $stats['active_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['active'];

        // Total invoices
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices");
        $stats['total_invoices'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Total revenue
        $stmt = $pdo->query("SELECT SUM(total_amount) as revenue FROM invoices WHERE status = 'paid'");
        $stats['total_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['revenue'] ?? 0;

        // Monthly revenue
        $stmt = $pdo->query("SELECT SUM(total_amount) as revenue FROM invoices WHERE status = 'paid' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stats['monthly_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['revenue'] ?? 0;

        // Total clients
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM clients");
        $stats['total_clients'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        return $stats;

    } catch (Exception $e) {
        logAdminError("Failed to get dashboard stats: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent activities for dashboard
 */
function getRecentActivities($limit = 10) {
    $pdo = getAdminDBConnection();
    if (!$pdo) return [];

    try {
        $stmt = $pdo->prepare("
            SELECT * FROM admin_activity_logs
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        logAdminError("Failed to get recent activities: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent users
 */
function getRecentUsers($limit = 5) {
    $pdo = getAdminDBConnection();
    if (!$pdo) return [];

    try {
        $stmt = $pdo->prepare("
            SELECT id, username, email, first_name, last_name, created_at
            FROM users
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        logAdminError("Failed to get recent users: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recent invoices
 */
function getRecentInvoices($limit = 5) {
    $pdo = getAdminDBConnection();
    if (!$pdo) return [];

    try {
        $stmt = $pdo->prepare("
            SELECT i.*, u.username, u.first_name, u.last_name
            FROM invoices i
            LEFT JOIN users u ON i.user_id = u.id
            ORDER BY i.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        logAdminError("Failed to get recent invoices: " . $e->getMessage());
        return [];
    }
}
?>
