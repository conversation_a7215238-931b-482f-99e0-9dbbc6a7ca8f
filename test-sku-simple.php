<?php
// اختبار بسيط لمشكلة SKU

echo "<h2>اختبار بسيط لمشكلة SKU</h2>";

// قراءة الملف مباشرة
$file_content = file_get_contents('sample-products-test.csv');
echo "<h3>محتوى الملف:</h3>";
echo "<pre>" . htmlspecialchars($file_content) . "</pre>";

// تقسيم إلى أسطر
$lines = explode("\n", $file_content);
echo "<h3>عدد الأسطر: " . count($lines) . "</h3>";

// تحليل السطر الأول (العناوين)
if (count($lines) > 0) {
    $headers_line = trim($lines[0]);
    echo "<h3>سطر العناوين:</h3>";
    echo "<p>'" . htmlspecialchars($headers_line) . "'</p>";
    
    // تقسيم بالفاصلة المنقوطة
    $headers = explode(';', $headers_line);
    echo "<h3>العناوين بعد التقسيم:</h3>";
    foreach ($headers as $i => $header) {
        echo "<p>[$i] '" . htmlspecialchars($header) . "'</p>";
    }
    
    // البحث عن SKU
    $sku_index = -1;
    foreach ($headers as $i => $header) {
        if (strtolower(trim($header)) === 'sku') {
            $sku_index = $i;
            break;
        }
    }
    
    echo "<h3>موضع عمود SKU: " . ($sku_index >= 0 ? $sku_index : "غير موجود") . "</h3>";
}

// تحليل السطر الثاني (أول منتج)
if (count($lines) > 1) {
    $product_line = trim($lines[1]);
    echo "<h3>سطر أول منتج:</h3>";
    echo "<p>'" . htmlspecialchars($product_line) . "'</p>";
    
    // تقسيم بالفاصلة المنقوطة
    $values = explode(';', $product_line);
    echo "<h3>القيم بعد التقسيم:</h3>";
    foreach ($values as $i => $value) {
        echo "<p>[$i] '" . htmlspecialchars($value) . "'</p>";
    }
    
    // قيمة SKU
    if ($sku_index >= 0 && isset($values[$sku_index])) {
        $sku_value = $values[$sku_index];
        echo "<h3>قيمة SKU:</h3>";
        echo "<p>القيمة الخام: '" . htmlspecialchars($sku_value) . "'</p>";
        echo "<p>بعد trim: '" . htmlspecialchars(trim($sku_value)) . "'</p>";
        echo "<p>هل فارغة؟ " . (empty(trim($sku_value)) ? "نعم ❌" : "لا ✅") . "</p>";
        echo "<p>الطول: " . strlen(trim($sku_value)) . "</p>";
    }
}

// اختبار منطق التحقق
echo "<h3>اختبار منطق التحقق:</h3>";
if ($sku_index >= 0 && isset($values[$sku_index])) {
    $test_sku = trim($values[$sku_index]);
    
    if (empty($test_sku)) {
        echo "<p style='color: red;'>❌ رمز المنتج (SKU) مطلوب ولا يمكن تركه فارغاً</p>";
    } else {
        echo "<p style='color: green;'>✅ SKU موجود: '" . htmlspecialchars($test_sku) . "'</p>";
    }
}
?>
