<?php
/**
 * Quick Fix for Installation Issue
 * This creates the required installation marker files
 */

echo "<h2>🔧 إصلاح سريع للنظام الإداري</h2>";

try {
    // Create installation markers
    $markerFile1 = __DIR__ . '/admin_installed.txt';
    $markerFile2 = __DIR__ . '/.installed';
    
    $timestamp = date('Y-m-d H:i:s');
    
    file_put_contents($markerFile1, $timestamp . " - تم تثبيت النظام الإداري بنجاح\n");
    file_put_contents($markerFile2, $timestamp . " - Admin system installed successfully\n");
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ تم إنشاء ملفات التثبيت بنجاح</h3>";
    echo "<p>تم إنشاء الملفات التالية:</p>";
    echo "<ul>";
    echo "<li>admin_installed.txt</li>";
    echo "<li>.installed</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test database connection
    require_once __DIR__ . '/../config/database.php';
    $db = new Database();
    $pdo = $db->connect();
    
    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    $adminTableExists = $stmt->rowCount() > 0;
    
    if ($adminTableExists) {
        // Check if default admin exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn();
        
        if ($adminExists) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ المستخدم الإداري موجود</h3>";
            echo "<p>يمكنك الآن الدخول للنظام الإداري</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>⚠️ المستخدم الإداري غير موجود</h3>";
            echo "<p>سيتم إنشاء المستخدم الإداري الآن...</p>";
            
            // Create default admin user
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (
                    username, email, password, first_name, last_name, role, 
                    permissions, is_active, email_verified
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $permissions = json_encode([
                'users' => ['view', 'create', 'edit', 'delete'],
                'invoices' => ['view', 'create', 'edit', 'delete'],
                'clients' => ['view', 'create', 'edit', 'delete'],
                'products' => ['view', 'create', 'edit', 'delete'],
                'reports' => ['view', 'export'],
                'settings' => ['view', 'edit'],
                'logs' => ['view'],
                'system' => ['view', 'edit']
            ]);
            
            $stmt->execute([
                'admin',
                '<EMAIL>',
                $hashedPassword,
                'مدير',
                'النظام',
                'super_admin',
                $permissions,
                1,
                1
            ]);
            
            echo "<p>✅ تم إنشاء المستخدم الإداري بنجاح</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ جدول admin_users غير موجود</h3>";
        echo "<p>يرجى تشغيل المثبت الكامل أولاً: <a href='web_install.php'>web_install.php</a></p>";
        echo "</div>";
    }
    
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔗 الخطوة التالية</h3>";
    echo "<p><a href='dashboard/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول لوحة التحكم</a></p>";
    echo "<p><a href='dashboard/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>صفحة تسجيل الدخول</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 بيانات تسجيل الدخول</h3>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</h3>";
    echo "<p>يرجى تشغيل المثبت الكامل: <a href='web_install.php'>web_install.php</a></p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
    direction: rtl;
}
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
