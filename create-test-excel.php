<?php
// Create a simple CSV file that mimics Excel export
$csv_content = "name;description;price;quantity;category;sku;status\n";
$csv_content .= "Test Product 1;First test product;100.50;10;Electronics;SKU-001;active\n";
$csv_content .= "Test Product 2;Second test product;200.75;5;Electronics;SKU-002;active\n";
$csv_content .= "Test Product 3;Third test product;150.25;8;Electronics;SKU-003;active\n";

file_put_contents('test-excel-format.csv', $csv_content);

echo "<h1>✅ Test Excel-format CSV Created</h1>";
echo "<h2>File: test-excel-format.csv</h2>";
echo "<h3>Content:</h3>";
echo "<pre>" . htmlspecialchars($csv_content) . "</pre>";

echo "<h3>File Info:</h3>";
echo "<div>File size: " . filesize('test-excel-format.csv') . " bytes</div>";
echo "<div>File exists: " . (file_exists('test-excel-format.csv') ? 'YES' : 'NO') . "</div>";

echo "<h3>Quick Test:</h3>";
require_once 'includes/SafeFileReader.php';

try {
    $reader = new SafeFileReader('test-excel-format.csv', 'test-excel-format.csv');
    $rows = $reader->readFile();
    
    echo "<div style='color: green;'>✅ File readable by SafeFileReader</div>";
    echo "<div>Rows: " . count($rows) . "</div>";
    
    if (count($rows) > 0) {
        echo "<h4>Headers:</h4>";
        foreach ($rows[0] as $i => $header) {
            echo "<div>[$i] '" . htmlspecialchars($header) . "'</div>";
        }
        
        if (count($rows) > 1) {
            echo "<h4>First Product:</h4>";
            foreach ($rows[1] as $i => $value) {
                echo "<div>[$i] '" . htmlspecialchars($value) . "'</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>🎯 Next Steps:</h2>";
echo "<ol>";
echo "<li>Use this file (test-excel-format.csv) to test the import</li>";
echo "<li>Check the debug output in the log viewer</li>";
echo "<li>Compare results with the SKU extraction test</li>";
echo "</ol>";
?>
