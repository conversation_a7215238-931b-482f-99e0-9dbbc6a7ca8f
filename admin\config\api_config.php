<?php
/**
 * Admin API Configuration
 * Configuration settings for the admin REST API
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not allowed');
}

/**
 * API Configuration Constants
 */
define('API_VERSION', 'v1');
define('API_BASE_URL', '/admin/api/v1');
define('API_RATE_LIMIT_DEFAULT', 1000); // requests per hour
define('API_RATE_LIMIT_WINDOW', 3600); // 1 hour in seconds
define('API_TOKEN_EXPIRY', 86400); // 24 hours in seconds
define('API_REFRESH_TOKEN_EXPIRY', 604800); // 7 days in seconds

/**
 * API Response Codes
 */
define('API_RESPONSE_CODES', [
    'SUCCESS' => 200,
    'CREATED' => 201,
    'NO_CONTENT' => 204,
    'BAD_REQUEST' => 400,
    'UNAUTHORIZED' => 401,
    'FORBIDDEN' => 403,
    'NOT_FOUND' => 404,
    'METHOD_NOT_ALLOWED' => 405,
    'VALIDATION_ERROR' => 422,
    'RATE_LIMIT_EXCEEDED' => 429,
    'INTERNAL_ERROR' => 500
]);

/**
 * API Response Messages (Arabic)
 */
define('API_MESSAGES', [
    'SUCCESS' => 'تم بنجاح',
    'CREATED' => 'تم الإنشاء بنجاح',
    'UPDATED' => 'تم التحديث بنجاح',
    'DELETED' => 'تم الحذف بنجاح',
    'NOT_FOUND' => 'العنصر المطلوب غير موجود',
    'UNAUTHORIZED' => 'غير مصرح لك بالوصول',
    'FORBIDDEN' => 'ليس لديك صلاحية للقيام بهذا الإجراء',
    'VALIDATION_ERROR' => 'خطأ في البيانات المدخلة',
    'RATE_LIMIT_EXCEEDED' => 'تم تجاوز الحد المسموح من الطلبات',
    'INTERNAL_ERROR' => 'حدث خطأ داخلي في النظام',
    'INVALID_API_KEY' => 'مفتاح API غير صحيح',
    'EXPIRED_TOKEN' => 'انتهت صلاحية الرمز المميز',
    'INVALID_CREDENTIALS' => 'بيانات تسجيل الدخول غير صحيحة'
]);

/**
 * API Endpoints Configuration
 */
define('API_ENDPOINTS', [
    'auth' => [
        'login' => ['POST', '/auth/login'],
        'logout' => ['POST', '/auth/logout'],
        'refresh' => ['POST', '/auth/refresh'],
        'me' => ['GET', '/auth/me']
    ],
    'users' => [
        'list' => ['GET', '/users'],
        'show' => ['GET', '/users/{id}'],
        'create' => ['POST', '/users'],
        'update' => ['PUT', '/users/{id}'],
        'delete' => ['DELETE', '/users/{id}'],
        'suspend' => ['POST', '/users/{id}/suspend'],
        'activate' => ['POST', '/users/{id}/activate'],
        'stats' => ['GET', '/users/stats']
    ],
    'invoices' => [
        'list' => ['GET', '/invoices'],
        'show' => ['GET', '/invoices/{id}'],
        'create' => ['POST', '/invoices'],
        'update' => ['PUT', '/invoices/{id}'],
        'delete' => ['DELETE', '/invoices/{id}'],
        'stats' => ['GET', '/invoices/stats']
    ],
    'clients' => [
        'list' => ['GET', '/clients'],
        'show' => ['GET', '/clients/{id}'],
        'create' => ['POST', '/clients'],
        'update' => ['PUT', '/clients/{id}'],
        'delete' => ['DELETE', '/clients/{id}']
    ],
    'products' => [
        'list' => ['GET', '/products'],
        'show' => ['GET', '/products/{id}'],
        'create' => ['POST', '/products'],
        'update' => ['PUT', '/products/{id}'],
        'delete' => ['DELETE', '/products/{id}'],
        'import' => ['POST', '/products/import']
    ],
    'reports' => [
        'financial' => ['GET', '/reports/financial'],
        'users' => ['GET', '/reports/users'],
        'invoices' => ['GET', '/reports/invoices'],
        'export' => ['POST', '/reports/export']
    ],
    'system' => [
        'stats' => ['GET', '/system/stats'],
        'settings' => ['GET', '/system/settings'],
        'update_settings' => ['PUT', '/system/settings'],
        'backup' => ['POST', '/system/backup'],
        'logs' => ['GET', '/system/logs']
    ],
    'templates' => [
        'list' => ['GET', '/templates'],
        'show' => ['GET', '/templates/{id}'],
        'create' => ['POST', '/templates'],
        'update' => ['PUT', '/templates/{id}'],
        'delete' => ['DELETE', '/templates/{id}']
    ],
    'api_keys' => [
        'list' => ['GET', '/api-keys'],
        'show' => ['GET', '/api-keys/{id}'],
        'create' => ['POST', '/api-keys'],
        'update' => ['PUT', '/api-keys/{id}'],
        'delete' => ['DELETE', '/api-keys/{id}'],
        'regenerate' => ['POST', '/api-keys/{id}/regenerate']
    ]
]);

/**
 * API Validation Rules
 */
define('API_VALIDATION_RULES', [
    'users' => [
        'create' => [
            'username' => 'required|string|min:3|max:50|unique:users',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8',
            'first_name' => 'required|string|max:50',
            'last_name' => 'required|string|max:50',
            'company_name' => 'string|max:100',
            'phone' => 'string|max:20',
            'subscription_plan' => 'in:free,basic,premium,enterprise'
        ],
        'update' => [
            'username' => 'string|min:3|max:50|unique:users,username,{id}',
            'email' => 'email|unique:users,email,{id}',
            'password' => 'string|min:8',
            'first_name' => 'string|max:50',
            'last_name' => 'string|max:50',
            'company_name' => 'string|max:100',
            'phone' => 'string|max:20',
            'subscription_plan' => 'in:free,basic,premium,enterprise'
        ]
    ],
    'invoices' => [
        'create' => [
            'user_id' => 'required|integer|exists:users,id',
            'client_id' => 'required|integer|exists:clients,id',
            'invoice_number' => 'required|string|max:50|unique:invoices',
            'title' => 'string|max:255',
            'issue_date' => 'required|date',
            'due_date' => 'date|after:issue_date',
            'total_amount' => 'required|numeric|min:0',
            'currency' => 'string|size:3'
        ]
    ],
    'api_keys' => [
        'create' => [
            'key_name' => 'required|string|max:100',
            'permissions' => 'required|array',
            'rate_limit' => 'integer|min:1|max:10000',
            'expires_at' => 'date|after:now'
        ]
    ]
]);

/**
 * API Helper Functions
 */

/**
 * Send JSON API Response
 */
function sendApiResponse($data = null, $message = '', $success = true, $code = 200, $errors = []) {
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('c')
    ];
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

/**
 * Send API Error Response
 */
function sendApiError($message, $code = 400, $errors = []) {
    sendApiResponse(null, $message, false, $code, $errors);
}

/**
 * Send API Success Response
 */
function sendApiSuccess($data = null, $message = 'تم بنجاح', $code = 200) {
    sendApiResponse($data, $message, true, $code);
}

/**
 * Get API Rate Limit Headers
 */
function getRateLimitHeaders($limit, $remaining, $reset) {
    return [
        'X-RateLimit-Limit' => $limit,
        'X-RateLimit-Remaining' => $remaining,
        'X-RateLimit-Reset' => $reset
    ];
}

/**
 * Validate API Input
 */
function validateApiInput($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $ruleArray = explode('|', $rule);
        $value = $data[$field] ?? null;
        
        foreach ($ruleArray as $singleRule) {
            if (strpos($singleRule, ':') !== false) {
                list($ruleName, $ruleValue) = explode(':', $singleRule, 2);
            } else {
                $ruleName = $singleRule;
                $ruleValue = null;
            }
            
            switch ($ruleName) {
                case 'required':
                    if (empty($value)) {
                        $errors[$field][] = "حقل {$field} مطلوب";
                    }
                    break;
                case 'string':
                    if (!is_string($value) && !is_null($value)) {
                        $errors[$field][] = "حقل {$field} يجب أن يكون نص";
                    }
                    break;
                case 'integer':
                    if (!is_numeric($value) && !is_null($value)) {
                        $errors[$field][] = "حقل {$field} يجب أن يكون رقم صحيح";
                    }
                    break;
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL) && !is_null($value)) {
                        $errors[$field][] = "حقل {$field} يجب أن يكون بريد إلكتروني صحيح";
                    }
                    break;
                case 'min':
                    if (strlen($value) < $ruleValue && !is_null($value)) {
                        $errors[$field][] = "حقل {$field} يجب أن يكون أكثر من {$ruleValue} أحرف";
                    }
                    break;
                case 'max':
                    if (strlen($value) > $ruleValue && !is_null($value)) {
                        $errors[$field][] = "حقل {$field} يجب أن يكون أقل من {$ruleValue} أحرف";
                    }
                    break;
            }
        }
    }
    
    return $errors;
}
?>
