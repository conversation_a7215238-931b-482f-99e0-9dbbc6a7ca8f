<?php
/**
 * Admin Sidebar Template
 * Navigation sidebar for admin pages
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not allowed');
}

$currentAdmin = getCurrentAdmin();
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));

// Function to check if menu item is active
function isMenuActive($page, $dir = null) {
    global $currentPage, $currentDir;
    
    if ($dir) {
        return $currentDir === $dir;
    }
    
    return $currentPage === $page;
}
?>

<div class="admin-sidebar">
    <nav class="nav flex-column pt-3">
        <!-- Dashboard -->
        <a class="nav-link <?php echo isMenuActive('index.php') ? 'active' : ''; ?>" href="/admin/dashboard/">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
        </a>
        
        <!-- User Management -->
        <?php if (hasAdminPermission('users', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'users') ? 'active' : ''; ?>" href="/admin/dashboard/users/">
            <i class="fas fa-users"></i>
            إدارة المستخدمين
            <?php
            // Get pending users count
            $pdo = getAdminDBConnection();
            if ($pdo) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 0");
                $pendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                if ($pendingCount > 0) {
                    echo '<span class="badge bg-warning ms-auto">' . $pendingCount . '</span>';
                }
            }
            ?>
        </a>
        <?php endif; ?>
        
        <!-- Invoice Management -->
        <?php if (hasAdminPermission('invoices', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'invoices') ? 'active' : ''; ?>" href="/admin/dashboard/invoices/">
            <i class="fas fa-file-invoice"></i>
            إدارة الفواتير
        </a>
        <?php endif; ?>
        
        <!-- Client Management -->
        <?php if (hasAdminPermission('clients', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'clients') ? 'active' : ''; ?>" href="/admin/dashboard/clients/">
            <i class="fas fa-address-book"></i>
            إدارة العملاء
        </a>
        <?php endif; ?>
        
        <!-- Product Management -->
        <?php if (hasAdminPermission('products', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'products') ? 'active' : ''; ?>" href="/admin/dashboard/products/">
            <i class="fas fa-box"></i>
            إدارة المنتجات
        </a>
        <?php endif; ?>
        
        <!-- Reports & Analytics -->
        <?php if (hasAdminPermission('reports', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'reports') ? 'active' : ''; ?>" href="/admin/dashboard/reports/">
            <i class="fas fa-chart-bar"></i>
            التقارير والإحصائيات
        </a>
        <?php endif; ?>
        
        <!-- Template Management -->
        <?php if (hasAdminPermission('templates', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'templates') ? 'active' : ''; ?>" href="/admin/dashboard/templates/">
            <i class="fas fa-file-alt"></i>
            إدارة القوالب
        </a>
        <?php endif; ?>
        
        <!-- System Settings -->
        <?php if (hasAdminPermission('system', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'system') ? 'active' : ''; ?>" href="/admin/dashboard/system/">
            <i class="fas fa-cogs"></i>
            إعدادات النظام
        </a>
        <?php endif; ?>
        
        <!-- API Management -->
        <?php if (hasAdminPermission('api', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'api') ? 'active' : ''; ?>" href="/admin/dashboard/api/">
            <i class="fas fa-key"></i>
            إدارة API
        </a>
        <?php endif; ?>
        
        <!-- Admin Management (Super Admin Only) -->
        <?php if (hasAdminPermission('admins', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'admins') ? 'active' : ''; ?>" href="/admin/dashboard/admins/">
            <i class="fas fa-user-shield"></i>
            إدارة المديرين
        </a>
        <?php endif; ?>
        
        <!-- Audit Logs -->
        <?php if (hasAdminPermission('audit', 'view')): ?>
        <a class="nav-link <?php echo isMenuActive('', 'audit') ? 'active' : ''; ?>" href="/admin/dashboard/audit/">
            <i class="fas fa-history"></i>
            سجلات المراجعة
        </a>
        <?php endif; ?>
        
        <!-- Divider -->
        <hr class="my-3 mx-3" style="border-color: #dee2e6;">
        
        <!-- Quick Actions -->
        <div class="px-3 mb-2">
            <small class="text-muted fw-bold">إجراءات سريعة</small>
        </div>
        
        <?php if (hasAdminPermission('users', 'create')): ?>
        <a class="nav-link" href="/admin/dashboard/users/create.php">
            <i class="fas fa-user-plus"></i>
            إضافة مستخدم جديد
        </a>
        <?php endif; ?>
        
        <?php if (hasAdminPermission('invoices', 'create')): ?>
        <a class="nav-link" href="/admin/dashboard/invoices/create.php">
            <i class="fas fa-plus-circle"></i>
            إنشاء فاتورة جديدة
        </a>
        <?php endif; ?>
        
        <?php if (hasAdminPermission('system', 'backup')): ?>
        <a class="nav-link" href="/admin/dashboard/system/backup.php">
            <i class="fas fa-download"></i>
            إنشاء نسخة احتياطية
        </a>
        <?php endif; ?>
        
        <!-- System Status -->
        <div class="px-3 mt-4">
            <div class="card border-0 bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title mb-2">حالة النظام</h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">الخادم</small>
                        <span class="badge bg-success">متصل</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">قاعدة البيانات</small>
                        <?php
                        $dbStatus = getAdminDBConnection() ? 'متصل' : 'غير متصل';
                        $dbClass = getAdminDBConnection() ? 'bg-success' : 'bg-danger';
                        ?>
                        <span class="badge <?php echo $dbClass; ?>"><?php echo $dbStatus; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">الإصدار</small>
                        <small class="text-primary"><?php echo ADMIN_CONFIG['version']; ?></small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- User Info -->
        <div class="px-3 mt-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-user-circle fa-2x"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0"><?php echo htmlspecialchars($currentAdmin['first_name'] . ' ' . $currentAdmin['last_name']); ?></h6>
                            <small class="opacity-75"><?php echo htmlspecialchars($currentAdmin['role']); ?></small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="opacity-75">
                            آخر تسجيل دخول: 
                            <?php echo $currentAdmin['last_login'] ? formatAdminDate($currentAdmin['last_login'], 'Y-m-d H:i') : 'غير محدد'; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </nav>
</div>

<style>
.admin-sidebar .nav-link {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-sidebar .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.admin-sidebar .card {
    font-size: 0.875rem;
}

.admin-sidebar hr {
    opacity: 0.3;
}

.admin-sidebar .nav-link:hover {
    text-decoration: none;
}

.admin-sidebar .nav-link.active {
    position: relative;
}

.admin-sidebar .nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--admin-primary);
    border-radius: 0 4px 4px 0;
}
</style>
