<?php
/**
 * Web-based Admin System Installation
 * Visit this page in your browser to install the admin system
 */

// Check if already installed
if (file_exists(__DIR__ . '/admin_installed.txt')) {
    die('Admin system is already installed. Delete admin_installed.txt to reinstall.');
}

$step = $_GET['step'] ?? 'start';
$errors = [];
$success = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'install') {
    try {
        // Include the main database configuration
        require_once __DIR__ . '/../config/database.php';
        
        // Create Database instance
        $db = new Database();
        $pdo = $db->connect();
        
        $success[] = "Database connection established successfully.";
        
        // Read and execute the admin schema
        $schemaFile = __DIR__ . '/database/admin_schema.sql';
        if (!file_exists($schemaFile)) {
            throw new Exception("Schema file not found: $schemaFile");
        }
        
        $schema = file_get_contents($schemaFile);
        if ($schema === false) {
            throw new Exception("Could not read schema file");
        }
        
        // Split the schema into individual statements
        $statements = array_filter(array_map('trim', explode(';', $schema)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore table already exists errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        $success[] = "Database schema installed successfully.";
        
        // Create admin directories
        $directories = [
            'admin/api',
            'admin/api/v1',
            'admin/api/v1/auth',
            'admin/api/v1/users',
            'admin/api/v1/invoices',
            'admin/api/v1/reports',
            'admin/dashboard/users',
            'admin/dashboard/invoices',
            'admin/dashboard/clients',
            'admin/dashboard/products',
            'admin/dashboard/reports',
            'admin/dashboard/system',
            'admin/dashboard/audit',
            'admin/assets',
            'admin/assets/css',
            'admin/assets/js',
            'admin/assets/images',
            'admin/logs',
            'admin/uploads'
        ];
        
        foreach ($directories as $dir) {
            $fullPath = __DIR__ . '/../' . $dir;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }
        }
        
        $success[] = "Directory structure created successfully.";
        
        // Create .htaccess files for security
        $htaccessContent = "Order Deny,Allow\nDeny from all\n";
        
        $protectedDirs = ['admin/config', 'admin/logs', 'admin/database'];
        foreach ($protectedDirs as $dir) {
            $htaccessFile = __DIR__ . '/../' . $dir . '/.htaccess';
            if (!file_exists($htaccessFile)) {
                file_put_contents($htaccessFile, $htaccessContent);
            }
        }
        
        $success[] = "Security files configured successfully.";
        
        // Create installation marker
        $markerFile = __DIR__ . '/admin_installed.txt';
        file_put_contents($markerFile, date('Y-m-d H:i:s') . " - Admin system installed successfully via web installer\n");
        
        $success[] = "Installation completed successfully!";
        $step = 'complete';
        
    } catch (Exception $e) {
        $errors[] = "Installation failed: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت النظام الإداري</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="install-card p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                        <h2 class="text-primary">تثبيت النظام الإداري</h2>
                        <p class="text-muted">نظام إدارة متعدد المستأجرين لمنصة الفواتير</p>
                    </div>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>حدثت أخطاء:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>تم بنجاح:</h6>
                            <ul class="mb-0">
                                <?php foreach ($success as $msg): ?>
                                    <li><?php echo htmlspecialchars($msg); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step === 'start'): ?>
                        <div class="text-center">
                            <h4 class="mb-4">مرحباً بك في معالج التثبيت</h4>
                            <p class="mb-4">سيقوم هذا المعالج بتثبيت النظام الإداري متعدد المستأجرين لمنصة الفواتير.</p>
                            
                            <div class="row text-start mb-4">
                                <div class="col-12">
                                    <h6>ما سيتم تثبيته:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>قاعدة بيانات النظام الإداري</li>
                                        <li><i class="fas fa-check text-success me-2"></i>هيكل المجلدات المطلوب</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ملفات الحماية والأمان</li>
                                        <li><i class="fas fa-check text-success me-2"></i>مستخدم إداري افتراضي</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <form method="POST" action="?step=install">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download me-2"></i>بدء التثبيت
                                </button>
                            </form>
                        </div>
                    
                    <?php elseif ($step === 'complete'): ?>
                        <div class="text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                <h3 class="text-success">تم التثبيت بنجاح!</h3>
                            </div>
                            
                            <div class="alert alert-info text-start">
                                <h6><i class="fas fa-info-circle me-2"></i>بيانات تسجيل الدخول:</h6>
                                <ul class="mb-0">
                                    <li><strong>الرابط:</strong> <a href="dashboard/" target="_blank">dashboard/</a></li>
                                    <li><strong>اسم المستخدم:</strong> admin</li>
                                    <li><strong>كلمة المرور:</strong> admin123</li>
                                    <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>مهم:</strong> يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="dashboard/" class="btn btn-success btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول النظام الإداري
                                </a>
                                <a href="../" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>العودة للموقع الرئيسي
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
