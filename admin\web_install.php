<?php
/**
 * Web-based Admin System Installation
 * Visit this page in your browser to install the admin system
 */

// Check if already installed
if (file_exists(__DIR__ . '/admin_installed.txt')) {
    die('Admin system is already installed. Delete admin_installed.txt to reinstall.');
}

$step = $_GET['step'] ?? 'start';
$errors = [];
$success = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'install') {
    try {
        // Include the main database configuration
        require_once __DIR__ . '/../config/database.php';

        // Create Database instance
        $db = new Database();
        $pdo = $db->connect();

        $success[] = "تم الاتصال بقاعدة البيانات بنجاح";

        // First, ensure main tables exist properly
        $mainTables = [
            'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                company_name VARCHAR(100),
                phone VARCHAR(20),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
                subscription_expires_at DATETIME NULL,
                is_active BOOLEAN DEFAULT TRUE,
                email_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_subscription (subscription_plan)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            'clients' => "
            CREATE TABLE IF NOT EXISTS clients (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                company VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                tax_number VARCHAR(50),
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            'invoices' => "
            CREATE TABLE IF NOT EXISTS invoices (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                client_id INT NOT NULL,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                title VARCHAR(255),
                issue_date DATE NOT NULL,
                due_date DATE,
                status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
                subtotal DECIMAL(10,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                discount_amount DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'SAR',
                notes TEXT,
                terms TEXT,
                template_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_client_id (client_id),
                INDEX idx_status (status),
                INDEX idx_invoice_number (invoice_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];

        // Create main tables first
        foreach ($mainTables as $tableName => $sql) {
            try {
                $pdo->exec($sql);
                $success[] = "تم إنشاء جدول {$tableName}";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw new Exception("خطأ في إنشاء جدول {$tableName}: " . $e->getMessage());
                }
                $success[] = "جدول {$tableName} موجود مسبقاً";
            }
        }

        // Now create admin tables
        $adminTables = [
            'admin_users' => "
            CREATE TABLE IF NOT EXISTS admin_users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                role ENUM('super_admin', 'admin', 'moderator', 'viewer') DEFAULT 'admin',
                permissions JSON,
                is_active BOOLEAN DEFAULT TRUE,
                avatar VARCHAR(255) NULL,
                phone VARCHAR(20) NULL,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret VARCHAR(32) NULL,
                email_verified BOOLEAN DEFAULT FALSE,
                email_verification_token VARCHAR(64) NULL,
                password_reset_token VARCHAR(64) NULL,
                password_reset_expires TIMESTAMP NULL,
                last_login TIMESTAMP NULL,
                last_login_ip VARCHAR(45) NULL,
                login_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            'admin_sessions' => "
            CREATE TABLE IF NOT EXISTS admin_sessions (
                id VARCHAR(128) PRIMARY KEY,
                admin_id INT NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
                INDEX idx_admin_id (admin_id),
                INDEX idx_expires_at (expires_at),
                INDEX idx_last_activity (last_activity)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            'admin_logs' => "
            CREATE TABLE IF NOT EXISTS admin_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                admin_id INT,
                action VARCHAR(100) NOT NULL,
                entity_type VARCHAR(50),
                entity_id INT,
                old_values JSON,
                new_values JSON,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                api_endpoint VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
                INDEX idx_admin_id (admin_id),
                INDEX idx_action (action),
                INDEX idx_entity (entity_type, entity_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            'admin_settings' => "
            CREATE TABLE IF NOT EXISTS admin_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                updated_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES admin_users(id) ON DELETE SET NULL,
                INDEX idx_setting_key (setting_key),
                INDEX idx_is_public (is_public)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];

        // Create admin tables
        foreach ($adminTables as $tableName => $sql) {
            try {
                $pdo->exec($sql);
                $success[] = "تم إنشاء جدول {$tableName}";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw new Exception("خطأ في إنشاء جدول {$tableName}: " . $e->getMessage());
                }
                $success[] = "جدول {$tableName} موجود مسبقاً";
            }
        }

        // Insert default admin user
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (
                username, email, password, first_name, last_name, role,
                permissions, is_active, email_verified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                password = VALUES(password),
                permissions = VALUES(permissions)
        ");

        $permissions = json_encode([
            'users' => ['view', 'create', 'edit', 'delete'],
            'invoices' => ['view', 'create', 'edit', 'delete'],
            'clients' => ['view', 'create', 'edit', 'delete'],
            'products' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'export'],
            'settings' => ['view', 'edit'],
            'logs' => ['view'],
            'system' => ['view', 'edit']
        ]);

        $stmt->execute([
            'admin',
            '<EMAIL>',
            $hashedPassword,
            'مدير',
            'النظام',
            'super_admin',
            $permissions,
            1,
            1
        ]);

        $success[] = "تم إنشاء المستخدم الإداري الافتراضي";

        // Create admin directories
        $directories = [
            'admin/api',
            'admin/api/v1',
            'admin/api/v1/auth',
            'admin/api/v1/users',
            'admin/api/v1/invoices',
            'admin/api/v1/reports',
            'admin/dashboard/users',
            'admin/dashboard/invoices',
            'admin/dashboard/clients',
            'admin/dashboard/products',
            'admin/dashboard/reports',
            'admin/dashboard/system',
            'admin/dashboard/audit',
            'admin/assets',
            'admin/assets/css',
            'admin/assets/js',
            'admin/assets/images',
            'admin/logs',
            'admin/uploads'
        ];

        foreach ($directories as $dir) {
            $fullPath = __DIR__ . '/../' . $dir;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }
        }

        $success[] = "تم إنشاء هيكل المجلدات بنجاح";

        // Create .htaccess files for security
        $htaccessContent = "Order Deny,Allow\nDeny from all\n";

        $protectedDirs = ['admin/config', 'admin/logs', 'admin/database'];
        foreach ($protectedDirs as $dir) {
            $htaccessFile = __DIR__ . '/../' . $dir . '/.htaccess';
            if (!file_exists($htaccessFile)) {
                file_put_contents($htaccessFile, $htaccessContent);
            }
        }

        $success[] = "تم تكوين ملفات الحماية بنجاح";

        // Insert default settings
        $defaultSettings = [
            ['system_name', 'نظام إدارة منصة الفواتير', 'string', 'اسم النظام الإداري', 1],
            ['system_version', '1.0.0', 'string', 'إصدار النظام', 1],
            ['maintenance_mode', 'false', 'boolean', 'وضع الصيانة', 0],
            ['max_login_attempts', '5', 'integer', 'عدد محاولات تسجيل الدخول المسموحة', 0],
            ['session_timeout', '3600', 'integer', 'مدة انتهاء الجلسة بالثواني', 0]
        ];

        $settingsStmt = $pdo->prepare("
            INSERT INTO admin_settings (setting_key, setting_value, setting_type, description, is_public)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");

        foreach ($defaultSettings as $setting) {
            $settingsStmt->execute($setting);
        }

        $success[] = "تم إدراج الإعدادات الافتراضية";

        // Create installation markers
        $markerFile1 = __DIR__ . '/admin_installed.txt';
        $markerFile2 = __DIR__ . '/.installed';
        file_put_contents($markerFile1, date('Y-m-d H:i:s') . " - تم تثبيت النظام الإداري بنجاح عبر المثبت الويب\n");
        file_put_contents($markerFile2, date('Y-m-d H:i:s') . " - Admin system installed successfully\n");

        $success[] = "تم الانتهاء من التثبيت بنجاح!";
        $step = 'complete';
        
    } catch (Exception $e) {
        $errors[] = "Installation failed: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت النظام الإداري</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="install-card p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                        <h2 class="text-primary">تثبيت النظام الإداري</h2>
                        <p class="text-muted">نظام إدارة متعدد المستأجرين لمنصة الفواتير</p>
                    </div>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>حدثت أخطاء:</h6>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>تم بنجاح:</h6>
                            <ul class="mb-0">
                                <?php foreach ($success as $msg): ?>
                                    <li><?php echo htmlspecialchars($msg); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step === 'start'): ?>
                        <div class="text-center">
                            <h4 class="mb-4">مرحباً بك في معالج التثبيت</h4>
                            <p class="mb-4">سيقوم هذا المعالج بتثبيت النظام الإداري متعدد المستأجرين لمنصة الفواتير.</p>
                            
                            <div class="row text-start mb-4">
                                <div class="col-12">
                                    <h6>ما سيتم تثبيته:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>قاعدة بيانات النظام الإداري</li>
                                        <li><i class="fas fa-check text-success me-2"></i>هيكل المجلدات المطلوب</li>
                                        <li><i class="fas fa-check text-success me-2"></i>ملفات الحماية والأمان</li>
                                        <li><i class="fas fa-check text-success me-2"></i>مستخدم إداري افتراضي</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <form method="POST" action="?step=install">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-download me-2"></i>بدء التثبيت
                                </button>
                            </form>
                        </div>
                    
                    <?php elseif ($step === 'complete'): ?>
                        <div class="text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                                <h3 class="text-success">تم التثبيت بنجاح!</h3>
                            </div>
                            
                            <div class="alert alert-info text-start">
                                <h6><i class="fas fa-info-circle me-2"></i>بيانات تسجيل الدخول:</h6>
                                <ul class="mb-0">
                                    <li><strong>الرابط:</strong> <a href="dashboard/" target="_blank">dashboard/</a></li>
                                    <li><strong>اسم المستخدم:</strong> admin</li>
                                    <li><strong>كلمة المرور:</strong> admin123</li>
                                    <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>مهم:</strong> يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول!
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="dashboard/" class="btn btn-success btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>دخول النظام الإداري
                                </a>
                                <a href="../" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>العودة للموقع الرئيسي
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
