<?php
/**
 * Admin System Configuration
 * Multi-Tenant Administrative Management System
 */

// Prevent direct access
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include main system configuration
require_once '../../config/database.php';

// Admin System Constants
define('ADMIN_VERSION', '1.0.0');
define('ADMIN_NAME', 'نظام إدارة منصة الفواتير');
define('ADMIN_URL', '/admin');
define('ADMIN_PATH', __DIR__ . '/..');

// Security Settings
define('ADMIN_SESSION_TIMEOUT', 3600); // 1 hour
define('ADMIN_MAX_LOGIN_ATTEMPTS', 5);
define('ADMIN_LOCKOUT_DURATION', 900); // 15 minutes
define('ADMIN_PASSWORD_MIN_LENGTH', 8);
define('ADMIN_REQUIRE_2FA', false);

// API Settings
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // requests per hour
define('API_TOKEN_EXPIRY', 86400); // 24 hours

// File Upload Settings
define('ADMIN_UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('ADMIN_ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'xlsx', 'csv']);

// Logging Settings
define('ADMIN_LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR, CRITICAL
define('ADMIN_LOG_MAX_SIZE', 50 * 1024 * 1024); // 50MB
define('ADMIN_LOG_RETENTION_DAYS', 90);

// Cache Settings
define('ADMIN_CACHE_ENABLED', true);
define('ADMIN_CACHE_TTL', 3600); // 1 hour

// Email Settings
define('ADMIN_EMAIL_FROM', '<EMAIL>');
define('ADMIN_EMAIL_NAME', 'نظام إدارة الفواتير');

/**
 * Admin Database Connection
 */
function getAdminDBConnection() {
    static $adminPdo = null;
    
    if ($adminPdo === null) {
        try {
            $database = new Database();
            $adminPdo = $database->connect();
        } catch (Exception $e) {
            logAdminError("Database connection failed: " . $e->getMessage());
            return null;
        }
    }
    
    return $adminPdo;
}

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && 
           isset($_SESSION['admin_username']) && 
           isset($_SESSION['admin_role']);
}

/**
 * Get current admin user
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    static $currentAdmin = null;
    
    if ($currentAdmin === null) {
        try {
            $pdo = getAdminDBConnection();
            $stmt = $pdo->prepare("
                SELECT id, username, email, first_name, last_name, role, permissions, 
                       avatar, phone, two_factor_enabled, last_login, created_at
                FROM admin_users 
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$_SESSION['admin_id']]);
            $currentAdmin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($currentAdmin && $currentAdmin['permissions']) {
                $currentAdmin['permissions'] = json_decode($currentAdmin['permissions'], true);
            }
        } catch (Exception $e) {
            logAdminError("Failed to get current admin: " . $e->getMessage());
            return null;
        }
    }
    
    return $currentAdmin;
}

/**
 * Check admin permission
 */
function hasAdminPermission($module, $action = 'view') {
    $admin = getCurrentAdmin();
    if (!$admin) return false;
    
    // Super admin has all permissions
    if ($admin['role'] === 'super_admin') {
        return true;
    }
    
    // Check specific permissions
    if (isset($admin['permissions'][$module])) {
        return in_array($action, $admin['permissions'][$module]);
    }
    
    return false;
}

/**
 * Require admin permission
 */
function requireAdminPermission($module, $action = 'view') {
    if (!hasAdminPermission($module, $action)) {
        http_response_code(403);
        die(json_encode([
            'success' => false,
            'message' => 'ليس لديك صلاحية للوصول إلى هذا المورد'
        ]));
    }
}

/**
 * Redirect admin to login if not authenticated
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        if (isAjaxRequest()) {
            http_response_code(401);
            die(json_encode([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ]));
        } else {
            header('Location: ' . ADMIN_URL . '/dashboard/login.php');
            exit;
        }
    }
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Sanitize input data
 */
function sanitizeAdminInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeAdminInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate admin session
 */
function validateAdminSession() {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['admin_last_activity'])) {
        if (time() - $_SESSION['admin_last_activity'] > ADMIN_SESSION_TIMEOUT) {
            destroyAdminSession();
            return false;
        }
    }
    
    // Update last activity
    $_SESSION['admin_last_activity'] = time();
    
    return true;
}

/**
 * Destroy admin session
 */
function destroyAdminSession() {
    $sessionKeys = ['admin_id', 'admin_username', 'admin_role', 'admin_last_activity'];
    foreach ($sessionKeys as $key) {
        unset($_SESSION[$key]);
    }
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $entityType = null, $entityId = null, $description = '', $oldValues = null, $newValues = null) {
    try {
        $admin = getCurrentAdmin();
        if (!$admin) return;
        
        $pdo = getAdminDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, entity_type, entity_id, old_values, new_values, description, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $admin['id'],
            $action,
            $entityType,
            $entityId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        logAdminError("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Log admin errors
 */
function logAdminError($message, $context = []) {
    $logFile = ADMIN_PATH . '/logs/admin_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] ERROR: {$message}{$contextStr}" . PHP_EOL;
    
    // Ensure logs directory exists
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Generate secure token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Format number for Arabic locale
 */
function formatArabicNumber($number, $decimals = 2) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * Get system statistics cache
 */
function getSystemStatsCache($key, $callback, $ttl = 3600) {
    if (!ADMIN_CACHE_ENABLED) {
        return $callback();
    }
    
    try {
        $pdo = getAdminDBConnection();
        $stmt = $pdo->prepare("SELECT cache_data FROM admin_stats_cache WHERE cache_key = ? AND expires_at > NOW()");
        $stmt->execute([$key]);
        $cached = $stmt->fetch();
        
        if ($cached) {
            return json_decode($cached['cache_data'], true);
        }
        
        // Generate new data
        $data = $callback();
        
        // Store in cache
        $stmt = $pdo->prepare("
            INSERT INTO admin_stats_cache (cache_key, cache_data, expires_at) 
            VALUES (?, ?, DATE_ADD(NOW(), INTERVAL ? SECOND))
            ON DUPLICATE KEY UPDATE 
                cache_data = VALUES(cache_data), 
                expires_at = VALUES(expires_at),
                updated_at = NOW()
        ");
        $stmt->execute([$key, json_encode($data), $ttl]);
        
        return $data;
    } catch (Exception $e) {
        logAdminError("Cache error: " . $e->getMessage());
        return $callback();
    }
}

// Initialize admin system
if (!file_exists(ADMIN_PATH . '/.installed')) {
    if (basename($_SERVER['PHP_SELF']) !== 'install_admin_system.php') {
        die('Admin system not installed. Please run the installation script first.');
    }
}
?>
