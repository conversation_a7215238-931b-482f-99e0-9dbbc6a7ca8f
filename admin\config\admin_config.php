<?php
/**
 * Admin System Configuration
 * Core configuration file for the administrative management system
 */

// Security check
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}

// Admin system paths
define('ADMIN_PATH', __DIR__ . '/..');
define('ADMIN_CONFIG_PATH', __DIR__);
define('ADMIN_INCLUDES_PATH', ADMIN_PATH . '/includes');
define('ADMIN_ASSETS_PATH', ADMIN_PATH . '/assets');
define('ADMIN_LOGS_PATH', ADMIN_PATH . '/logs');

// Include required configurations
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/permissions.php';
require_once __DIR__ . '/api_config.php';

// Session configuration
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on');
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    session_start();
}

// Admin system configuration
define('ADMIN_CONFIG', [
    'system_name' => 'نظام إدارة منصة الفواتير',
    'version' => '1.0.0',
    'timezone' => 'Asia/Riyadh',
    'language' => 'ar',
    'direction' => 'rtl',
    'max_login_attempts' => 5,
    'lockout_duration' => 900, // 15 minutes
    'password_min_length' => 8,
    'session_timeout' => 3600, // 1 hour
    'pagination_limit' => 20,
    'upload_max_size' => '10M',
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
    'backup_retention_days' => 30,
    'log_retention_days' => 90,
    'enable_two_factor' => false,
    'enable_audit_log' => true,
    'enable_api_rate_limit' => true
]);

// Set timezone
date_default_timezone_set(ADMIN_CONFIG['timezone']);

/**
 * Database Connection for Admin System
 */
function getAdminDBConnection() {
    static $adminPdo = null;

    if ($adminPdo === null) {
        try {
            $database = new Database();
            $adminPdo = $database->connect();
        } catch (Exception $e) {
            logAdminError("Database connection failed: " . $e->getMessage());
            return null;
        }
    }

    return $adminPdo;
}

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && 
           isset($_SESSION['admin_username']) && 
           isset($_SESSION['admin_role']);
}

/**
 * Get current admin user
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    static $currentAdmin = null;
    
    if ($currentAdmin === null) {
        try {
            $pdo = getAdminDBConnection();
            $stmt = $pdo->prepare("
                SELECT id, username, email, first_name, last_name, role, permissions, 
                       avatar, phone, two_factor_enabled, last_login, created_at
                FROM admin_users 
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$_SESSION['admin_id']]);
            $currentAdmin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($currentAdmin && $currentAdmin['permissions']) {
                $currentAdmin['permissions'] = json_decode($currentAdmin['permissions'], true);
            }
        } catch (Exception $e) {
            logAdminError("Failed to get current admin: " . $e->getMessage());
            return null;
        }
    }
    
    return $currentAdmin;
}

/**
 * Check admin permission
 */
function hasAdminPermission($module, $action = 'view') {
    $admin = getCurrentAdmin();
    if (!$admin) return false;
    
    // Super admin has all permissions
    if ($admin['role'] === 'super_admin') {
        return true;
    }
    
    // Check specific permissions
    if (isset($admin['permissions'][$module])) {
        return in_array($action, $admin['permissions'][$module]);
    }
    
    return false;
}

/**
 * Require admin permission
 */
function requireAdminPermission($module, $action = 'view') {
    if (!hasAdminPermission($module, $action)) {
        http_response_code(403);
        die(json_encode([
            'success' => false,
            'message' => 'ليس لديك صلاحية للوصول إلى هذا المورد'
        ]));
    }
}

/**
 * Redirect admin to login if not authenticated
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        if (isAjaxRequest()) {
            http_response_code(401);
            die(json_encode([
                'success' => false,
                'message' => 'يجب تسجيل الدخول أولاً'
            ]));
        } else {
            header('Location: ' . ADMIN_URL . '/dashboard/login.php');
            exit;
        }
    }
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Sanitize input data
 */
function sanitizeAdminInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeAdminInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate admin session
 */
function validateAdminSession() {
    if (!isAdminLoggedIn()) {
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['admin_last_activity'])) {
        if (time() - $_SESSION['admin_last_activity'] > ADMIN_SESSION_TIMEOUT) {
            destroyAdminSession();
            return false;
        }
    }
    
    // Update last activity
    $_SESSION['admin_last_activity'] = time();
    
    return true;
}

/**
 * Destroy admin session
 */
function destroyAdminSession() {
    $sessionKeys = ['admin_id', 'admin_username', 'admin_role', 'admin_last_activity'];
    foreach ($sessionKeys as $key) {
        unset($_SESSION[$key]);
    }
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $entityType = null, $entityId = null, $description = '', $oldValues = null, $newValues = null) {
    try {
        $admin = getCurrentAdmin();
        if (!$admin) return;
        
        $pdo = getAdminDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, entity_type, entity_id, old_values, new_values, description, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $admin['id'],
            $action,
            $entityType,
            $entityId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        logAdminError("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Log admin errors
 */
function logAdminError($message, $context = []) {
    $logFile = ADMIN_PATH . '/logs/admin_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[{$timestamp}] ERROR: {$message}{$contextStr}" . PHP_EOL;
    
    // Ensure logs directory exists
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Generate secure token
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Format number for Arabic locale
 */
function formatArabicNumber($number, $decimals = 2) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * Get system statistics cache
 */
function getSystemStatsCache($key, $callback, $ttl = 3600) {
    if (!ADMIN_CACHE_ENABLED) {
        return $callback();
    }
    
    try {
        $pdo = getAdminDBConnection();
        $stmt = $pdo->prepare("SELECT cache_data FROM admin_stats_cache WHERE cache_key = ? AND expires_at > NOW()");
        $stmt->execute([$key]);
        $cached = $stmt->fetch();
        
        if ($cached) {
            return json_decode($cached['cache_data'], true);
        }
        
        // Generate new data
        $data = $callback();
        
        // Store in cache
        $stmt = $pdo->prepare("
            INSERT INTO admin_stats_cache (cache_key, cache_data, expires_at) 
            VALUES (?, ?, DATE_ADD(NOW(), INTERVAL ? SECOND))
            ON DUPLICATE KEY UPDATE 
                cache_data = VALUES(cache_data), 
                expires_at = VALUES(expires_at),
                updated_at = NOW()
        ");
        $stmt->execute([$key, json_encode($data), $ttl]);
        
        return $data;
    } catch (Exception $e) {
        logAdminError("Cache error: " . $e->getMessage());
        return $callback();
    }
}

// Initialize admin system
if (!file_exists(ADMIN_PATH . '/.installed')) {
    if (basename($_SERVER['PHP_SELF']) !== 'install_admin_system.php') {
        die('Admin system not installed. Please run the installation script first.');
    }
}
?>
