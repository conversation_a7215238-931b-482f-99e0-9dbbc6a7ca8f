<?php
require_once 'config/config.php';
require_once 'includes/SafeFileReader.php';

// محاولة تحميل PhpSpreadsheet إذا كانت متوفرة
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

$success = '';
$error = '';
$preview_data = [];
$import_results = [];

// معالجة تحميل الملف
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();
        
        if (isset($_POST['action']) && $_POST['action'] == 'preview') {
            // معاينة الملف
            if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('يرجى اختيار ملف Excel صحيح');
            }

            $file = $_FILES['excel_file'];
            $allowed_types = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/csv',
                'application/csv',
                'text/plain',
                'application/octet-stream' // للملفات التي لا يتم التعرف على نوعها
            ];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['xlsx', 'xls', 'csv'];

            // التحقق من الامتداد أولاً (أكثر موثوقية من MIME type)
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx, .xls) أو CSV (.csv)');
            }

            // التحقق من MIME type كفحص إضافي (ليس إجباري)
            if (!in_array($file['type'], $allowed_types)) {
                // تحذير فقط، لا نوقف العملية
                error_log("تحذير: نوع MIME غير متوقع: " . $file['type'] . " للملف: " . $file['name']);
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }

            // قراءة الملف باستخدام SafeFileReader
            try {
                $reader = new SafeFileReader($file['tmp_name'], $file['name']);
                $reader->validateFile();
                $rows = $reader->readFile();
            } catch (Exception $e) {
                throw new Exception('فشل في قراءة الملف: ' . $e->getMessage());
            }
            
            if (empty($rows)) {
                throw new Exception('الملف فارغ أو لا يحتوي على بيانات');
            }
            
            // التحقق من وجود العناوين المطلوبة
            $headers = array_map(function($header) {
                // تنظيف العنوان من المسافات والأحرف الخاصة
                $cleaned = trim($header);
                $cleaned = preg_replace('/\s+/', ' ', $cleaned); // توحيد المسافات
                $cleaned = strtolower($cleaned); // تحويل لأحرف صغيرة
                return $cleaned;
            }, $rows[0]);

            $required_headers = ['name', 'price', 'quantity', 'sku'];
            $optional_headers = ['description', 'category', 'status'];
            $all_headers = array_merge($required_headers, $optional_headers);

            // إنشاء خريطة للعناوين مع البدائل المحتملة
            $header_map = [
                'name' => ['name', 'اسم المنتج', 'product name', 'اسم', 'المنتج'],
                'price' => ['price', 'السعر', 'سعر', 'cost', 'amount'],
                'quantity' => ['quantity', 'الكمية', 'كمية', 'qty', 'stock'],
                'description' => ['description', 'الوصف', 'وصف', 'desc'],
                'category' => ['category', 'الفئة', 'فئة', 'cat'],
                'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code'],
                'status' => ['status', 'الحالة', 'حالة', 'state']
            ];

            // البحث عن العناوين مع مراعاة البدائل
            $found_headers = [];
            $header_positions = [];

            // Debug: Log all headers
            error_log("DEBUG HEADERS: " . print_r($headers, true));

            foreach ($header_map as $standard_name => $alternatives) {
                $found = false;
                foreach ($alternatives as $alt) {
                    $alt_cleaned = strtolower(trim($alt));
                    $position = array_search($alt_cleaned, $headers);
                    if ($position !== false) {
                        $found_headers[$standard_name] = $position;
                        $header_positions[$position] = $standard_name;
                        $found = true;

                        // Debug: Log header mapping for SKU
                        if ($standard_name === 'sku') {
                            error_log("DEBUG SKU HEADER: Found '$standard_name' at position $position, matched '$alt_cleaned' with header at position $position");
                        }

                        break;
                    }
                }
                if (!$found && in_array($standard_name, $required_headers)) {
                    // لم نجد العنوان المطلوب
                    if ($standard_name === 'sku') {
                        error_log("DEBUG SKU HEADER: NOT FOUND! Alternatives: " . implode(', ', $alternatives));
                        error_log("DEBUG SKU HEADER: Available headers: " . implode(', ', $headers));
                    }
                }
            }

            // التحقق من العناوين المطلوبة
            $missing_headers = [];
            foreach ($required_headers as $header) {
                if (!isset($found_headers[$header])) {
                    $missing_headers[] = $header;
                }
            }

            if (!empty($missing_headers)) {
                // عرض تفاصيل أكثر للمساعدة في التشخيص
                $debug_info = "العناوين الموجودة في الملف: " . implode(', ', array_map(function($h, $i) {
                    return "[$i] '$h'";
                }, $rows[0], array_keys($rows[0])));

                $missing_headers_ar = [];
                foreach ($missing_headers as $header) {
                    switch ($header) {
                        case 'name': $missing_headers_ar[] = 'اسم المنتج (name)'; break;
                        case 'price': $missing_headers_ar[] = 'السعر (price)'; break;
                        case 'quantity': $missing_headers_ar[] = 'الكمية (quantity)'; break;
                        case 'sku': $missing_headers_ar[] = 'رمز المنتج (sku) - إجباري'; break;
                        default: $missing_headers_ar[] = $header; break;
                    }
                }

                throw new Exception('العناوين المطلوبة مفقودة: ' . implode(', ', $missing_headers_ar) . "\n\n" . $debug_info);
            }

            // التحقق من وجود عناوين غير معروفة (تحذير فقط، لا نوقف العملية)
            $unknown_headers = [];
            foreach ($headers as $index => $header) {
                if (!isset($header_positions[$index]) && !empty($header)) {
                    $unknown_headers[] = $header;
                }
            }

            if (!empty($unknown_headers)) {
                // تحذير فقط، لا نوقف العملية
                $warning = 'تحذير: عناوين غير معروفة في الملف (سيتم تجاهلها): ' . implode(', ', $unknown_headers);
                // يمكن إضافة التحذير لاحقاً في واجهة المستخدم
            }
            
            // معالجة البيانات
            $preview_data = [];
            $errors = [];

            for ($i = 1; $i < count($rows); $i++) {
                $row = $rows[$i];
                if (empty(array_filter($row))) continue; // تجاهل الصفوف الفارغة

                // Debug: Log row data for first few rows
                if ($i <= 3) {
                    error_log("DEBUG ROW $i: " . print_r($row, true));
                }

                $product = [];
                $row_errors = [];

                // استخدام الخريطة لاستخراج البيانات
                foreach ($found_headers as $standard_name => $position) {
                    $value = isset($row[$position]) ? trim($row[$position]) : '';

                    // Debug: Log data extraction for SKU
                    if ($standard_name === 'sku') {
                        error_log("DEBUG SKU EXTRACTION: Row $i, Position $position, Raw='" . ($row[$position] ?? 'NULL') . "', Trimmed='" . $value . "'");
                    }

                    switch ($standard_name) {
                        case 'name':
                            if (empty($value)) {
                                $row_errors[] = 'اسم المنتج مطلوب';
                            }
                            $product['name'] = $value;
                            break;
                            
                        case 'price':
                            if (empty($value)) {
                                $row_errors[] = 'السعر مطلوب';
                                $product['price'] = 0;
                            } else {
                                $price = (float)str_replace(',', '', $value); // إزالة الفواصل
                                if ($price < 0) {
                                    $row_errors[] = 'السعر يجب أن يكون أكبر من أو يساوي صفر';
                                } elseif (!is_numeric($value) && !is_numeric(str_replace(',', '', $value))) {
                                    $row_errors[] = 'السعر يجب أن يكون رقم صحيح';
                                }
                                $product['price'] = $price;
                            }
                            break;

                        case 'quantity':
                            if (empty($value)) {
                                $row_errors[] = 'الكمية مطلوبة';
                                $product['quantity'] = 0;
                            } else {
                                $quantity = (int)$value;
                                if ($quantity < 0) {
                                    $row_errors[] = 'الكمية يجب أن تكون أكبر من أو يساوي صفر';
                                } elseif (!is_numeric($value)) {
                                    $row_errors[] = 'الكمية يجب أن تكون رقم صحيح';
                                }
                                $product['quantity'] = $quantity;
                            }
                            break;
                            
                        case 'description':
                            $product['description'] = $value;
                            break;
                            
                        case 'category':
                            $product['category'] = $value;
                            break;
                            
                        case 'sku':
                            // تنظيف وتحليل قيمة SKU
                            $cleaned_sku = trim($value);
                            $product['sku'] = !empty($cleaned_sku) ? $cleaned_sku : '';
                            $product['sku_source'] = !empty($cleaned_sku) ? 'provided' : 'missing';
                            break;
                            
                        case 'status':
                            $status = trim(strtolower($value));
                            $valid_statuses = ['active', 'inactive', 'نشط', 'غير نشط', ''];
                            if (!in_array($status, $valid_statuses)) {
                                $row_errors[] = 'الحالة يجب أن تكون: active, inactive, نشط، أو غير نشط';
                            }
                            // تحويل القيم العربية والافتراضية
                            if ($status == 'نشط' || $status == 'active' || empty($status)) {
                                $product['status'] = 'active';
                            } else {
                                $product['status'] = 'inactive';
                            }
                            break;
                    }
                }
                
                // التحقق من وجود SKU (إجباري)
                static $used_skus = [];

                // Debug: Log SKU processing
                error_log("DEBUG SKU: Row $i, SKU='" . ($product['sku'] ?? 'NULL') . "', Empty=" . (empty($product['sku']) ? 'YES' : 'NO'));

                if (empty($product['sku'])) {
                    // SKU مفقود - رفض الصف
                    $row_errors[] = "رمز المنتج (SKU) مطلوب ولا يمكن تركه فارغاً [DEBUG: SKU='" . ($product['sku'] ?? 'NULL') . "']";
                    $product['sku_source'] = 'missing';
                } else {
                    // SKU موجود - التحقق من صحته
                    $current_sku = trim($product['sku']);

                    // التحقق من صحة تنسيق SKU
                    if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $current_sku)) {
                        $row_errors[] = "رمز المنتج '$current_sku' يحتوي على أحرف غير صالحة (يُسمح بالأحرف والأرقام والشرطات فقط)";
                    }

                    // التحقق من طول SKU
                    if (strlen($current_sku) > 100) {
                        $row_errors[] = "رمز المنتج طويل جداً (الحد الأقصى 100 حرف)";
                    }

                    // التحقق من تكرار في قاعدة البيانات
                    if (checkSKUExists($pdo, $user_id, $current_sku)) {
                        $row_errors[] = "رمز المنتج '$current_sku' موجود بالفعل في قاعدة البيانات";
                    }

                    // التحقق من تكرار في نفس الملف
                    if (in_array($current_sku, $used_skus)) {
                        $row_errors[] = "رمز المنتج '$current_sku' مكرر في نفس الملف";
                    } else {
                        $used_skus[] = $current_sku;
                    }

                    $product['sku_source'] = 'provided';
                }

                // التحقق من طول النصوص
                if (strlen($product['name']) > 255) {
                    $row_errors[] = 'اسم المنتج طويل جداً (الحد الأقصى 255 حرف)';
                }

                if (!empty($product['category']) && strlen($product['category']) > 100) {
                    $row_errors[] = 'اسم الفئة طويل جداً (الحد الأقصى 100 حرف)';
                }
                
                $product['row_number'] = $i + 1;
                $product['errors'] = $row_errors;
                $product['valid'] = empty($row_errors);
                
                $preview_data[] = $product;
            }
            
            if (empty($preview_data)) {
                throw new Exception('لا توجد بيانات صحيحة للاستيراد');
            }
            
            // حفظ البيانات في الجلسة للاستيراد لاحقاً
            $_SESSION['import_preview'] = $preview_data;
            
        } elseif (isset($_POST['action']) && $_POST['action'] == 'import') {
            // تنفيذ الاستيراد
            if (!isset($_SESSION['import_preview'])) {
                throw new Exception('لا توجد بيانات للاستيراد. يرجى معاينة الملف أولاً');
            }
            
            $preview_data = $_SESSION['import_preview'];
            $imported_count = 0;
            $skipped_count = 0;
            $import_results = [];
            
            $stmt = $pdo->prepare("
                INSERT INTO products (user_id, name, description, price, quantity, category, sku, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            // إضافة تسجيل للتشخيص
            error_log("بدء استيراد " . count($preview_data) . " منتج للمستخدم ID: $user_id");

            foreach ($preview_data as $product) {
                if (!$product['valid']) {
                    $skipped_count++;
                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'skipped',
                        'message' => 'تم تجاهل الصف بسبب أخطاء: ' . implode(', ', $product['errors'])
                    ];
                    continue;
                }
                
                try {
                    // التأكد من وجود SKU صالح
                    if (empty($product['sku'])) {
                        throw new Exception('رمز المنتج (SKU) مطلوب');
                    }

                    $final_sku = trim($product['sku']);

                    $stmt->execute([
                        $user_id,
                        $product['name'],
                        $product['description'] ?? '',
                        $product['price'],
                        $product['quantity'],
                        $product['category'] ?? '',
                        $final_sku,
                        $product['status'] ?? 'active'
                    ]);
                    
                    $imported_count++;
                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'success',
                        'message' => 'تم الاستيراد بنجاح'
                    ];
                    
                } catch (Exception $e) {
                    $skipped_count++;

                    // تحليل نوع الخطأ لتقديم رسالة أوضح
                    $error_message = $e->getMessage();
                    $user_friendly_message = 'خطأ: ' . $error_message;

                    // التعامل مع أخطاء SKU المكررة
                    if (strpos($error_message, 'Duplicate entry') !== false && strpos($error_message, 'unique_user_sku') !== false) {
                        $user_friendly_message = "رمز المنتج مكرر: '" . trim($product['sku']) . "' - يرجى استخدام رمز فريد";
                    }

                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'error',
                        'message' => $user_friendly_message
                    ];
                }
            }
            
            unset($_SESSION['import_preview']);
            
            $success = "تم استيراد {$imported_count} منتج بنجاح. تم تجاهل {$skipped_count} منتج.";
            
            // إعادة توجيه إلى صفحة المنتجات بعد 5 ثوانٍ
            if ($imported_count > 0) {
                header("refresh:5;url=products.php");
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        unset($_SESSION['import_preview']);
    }
}

/**
 * إنشاء SKU فريد تلقائياً مع دعم النصوص العربية والإنجليزية
 */
function generateUniqueSKU($pdo, $user_id, $product_name) {
    $base_sku = generateSKUBase($product_name);
    $timestamp = substr(time(), -6); // آخر 6 أرقام من timestamp
    $counter = 1;

    do {
        $sku = $base_sku . '-' . $timestamp . str_pad($counter, 2, '0', STR_PAD_LEFT);

        // التحقق من عدم وجود SKU مشابه للمستخدم الحالي
        if (!checkSKUExists($pdo, $user_id, $sku)) {
            return $sku;
        }

        $counter++;

        // إذا وصلنا للحد الأقصى، نغير timestamp
        if ($counter > 99) {
            $timestamp = substr(time() + rand(1, 1000), -6);
            $counter = 1;
        }

    } while ($counter <= 99);

    // إذا فشل كل شيء، استخدم UUID فريد
    return 'PRD-' . strtoupper(substr(uniqid(), -8));
}

/**
 * إنشاء الجزء الأساسي من SKU من اسم المنتج
 */
function generateSKUBase($product_name) {
    $clean_name = trim($product_name);

    // محاولة استخراج أحرف إنجليزية أو أرقام أولاً
    $english_chars = preg_replace('/[^a-zA-Z0-9]/', '', $clean_name);

    if (strlen($english_chars) >= 3) {
        return strtoupper(substr($english_chars, 0, 3));
    }

    // إذا لم توجد أحرف إنجليزية كافية، استخدم الأحرف الأولى من الكلمات
    $words = preg_split('/\s+/', $clean_name);
    $initials = '';

    foreach ($words as $word) {
        $word = trim($word);
        if (!empty($word)) {
            // استخراج أول حرف من كل كلمة
            $first_char = mb_substr($word, 0, 1, 'UTF-8');

            // تحويل الأحرف العربية إلى إنجليزية
            $transliterated = transliterateArabicChar($first_char);
            $initials .= $transliterated;

            if (strlen($initials) >= 3) break;
        }
    }

    // إذا لم نحصل على 3 أحرف، أضف أحرف إضافية
    if (strlen($initials) < 3) {
        $initials = str_pad($initials, 3, 'X');
    }

    return strtoupper(substr($initials, 0, 3));
}

/**
 * تحويل الأحرف العربية إلى أحرف إنجليزية مقابلة
 */
function transliterateArabicChar($char) {
    $arabic_to_english = [
        'ا' => 'A', 'أ' => 'A', 'إ' => 'A', 'آ' => 'A',
        'ب' => 'B', 'ت' => 'T', 'ث' => 'TH', 'ج' => 'J',
        'ح' => 'H', 'خ' => 'KH', 'د' => 'D', 'ذ' => 'DH',
        'ر' => 'R', 'ز' => 'Z', 'س' => 'S', 'ش' => 'SH',
        'ص' => 'S', 'ض' => 'D', 'ط' => 'T', 'ظ' => 'Z',
        'ع' => 'A', 'غ' => 'GH', 'ف' => 'F', 'ق' => 'Q',
        'ك' => 'K', 'ل' => 'L', 'م' => 'M', 'ن' => 'N',
        'ه' => 'H', 'و' => 'W', 'ي' => 'Y', 'ى' => 'Y',
        'ة' => 'H', 'ء' => 'A'
    ];

    return $arabic_to_english[$char] ?? strtoupper($char);
}

/**
 * التحقق من وجود SKU للمستخدم المحدد
 */
function checkSKUExists($pdo, $user_id, $sku) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ? AND sku = ?");
    $stmt->execute([$user_id, $sku]);
    return $stmt->fetchColumn() > 0;
}

/**
 * تحديد SKU النهائي للاستيراد مع معالجة جميع الحالات
 */
function determineFinalSKU($pdo, $user_id, $product) {
    // إذا كان SKU مُقدم من المستخدم وصالح
    if (isset($product['sku_source']) && $product['sku_source'] === 'provided' && !empty($product['sku'])) {
        $provided_sku = trim($product['sku']);

        // التحقق من عدم وجوده في قاعدة البيانات
        if (!checkSKUExists($pdo, $user_id, $provided_sku)) {
            return $provided_sku;
        }

        // إذا كان موجوداً، استخدم SKU المقترح إن وُجد
        if (isset($product['suggested_sku']) && !empty($product['suggested_sku'])) {
            return $product['suggested_sku'];
        }
    }

    // إذا كان هناك SKU مُولد مسبقاً في المعاينة
    if (isset($product['preview_sku']) && !empty($product['preview_sku'])) {
        $preview_sku = $product['preview_sku'];

        // التحقق من أنه ما زال فريداً
        if (!checkSKUExists($pdo, $user_id, $preview_sku)) {
            return $preview_sku;
        }
    }

    // في جميع الحالات الأخرى، إنشاء SKU جديد
    return generateUniqueSKU($pdo, $user_id, $product['name']);
}

/**
 * تحسين عرض رسائل النتائج مع تفاصيل SKU
 */
function formatImportMessage($status, $product_name, $sku_info = '') {
    $icons = [
        'success' => '<i class="fas fa-check-circle text-success"></i>',
        'error' => '<i class="fas fa-times-circle text-danger"></i>',
        'warning' => '<i class="fas fa-exclamation-triangle text-warning"></i>'
    ];

    $icon = $icons[$status] ?? $icons['error'];
    $message = "$icon <strong>" . htmlspecialchars($product_name) . "</strong>";

    if (!empty($sku_info)) {
        $message .= " - $sku_info";
    }

    return $message;
}

$page_title = 'استيراد المنتجات من Excel';
include 'includes/header.php';
?>

<div class="container py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <?php if (!empty($import_results) && count(array_filter($import_results, function($r) { return $r['status'] == 'success'; })) > 0): ?>
                <div class="mt-2">
                    <small>سيتم توجيهك إلى صفحة المنتجات خلال ثوانٍ...</small>
                </div>
            <?php endif; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-file-excel text-success me-2"></i>
                استيراد المنتجات من Excel
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="products.php">المنتجات</a></li>
                    <li class="breadcrumb-item active">استيراد Excel</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة إلى المنتجات
            </a>
            <div class="btn-group">
                <a href="download-simple-excel-template.php" class="btn btn-info">
                    <i class="fas fa-download me-2"></i>تحميل نموذج Excel
                </a>
                <button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                    <span class="visually-hidden">خيارات إضافية</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="download-simple-excel-template.php">
                        <i class="fas fa-file-excel me-2"></i>نموذج Excel (موصى به)
                    </a></li>
                    <li><a class="dropdown-item" href="download-excel-template.php">
                        <i class="fas fa-file-csv me-2"></i>نموذج CSV متوافق
                    </a></li>
                </ul>
            </div>
            <a href="excel-import-guide.php" class="btn btn-outline-primary">
                <i class="fas fa-book me-2"></i>دليل الاستيراد
            </a>
            <a href="convert-excel-to-csv.php" class="btn btn-outline-success">
                <i class="fas fa-exchange-alt me-2"></i>تحويل Excel إلى CSV
            </a>
            <a href="debug-file-upload.php" class="btn btn-outline-warning">
                <i class="fas fa-bug me-2"></i>تشخيص المشاكل
            </a>
        </div>
    </div>

    <?php if (empty($preview_data) && empty($import_results)): ?>
        <!-- نموذج رفع الملف -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>رفع ملف Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <input type="hidden" name="action" value="preview">

                            <div class="mb-4">
                                <label for="excel_file" class="form-label">اختر ملف Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file"
                                       accept=".xlsx,.xls,.csv" required>
                                <div class="form-text">
                                    الملفات المدعومة: .xlsx, .xls, .csv | الحد الأقصى: 5 ميجابايت
                                </div>
                            </div>

                            <div class="alert alert-warning border-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>تحديث مهم - SKU إجباري:</h6>
                                <p class="mb-2"><strong>اعتباراً من الآن، رمز المنتج (SKU) أصبح إجبارياً لجميع المنتجات.</strong></p>
                                <p class="mb-2">يجب أن يحتوي كل منتج على رمز فريد يتكون من أحرف وأرقام وشرطات فقط.</p>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>تنسيق الملف المطلوب:</h6>
                                <p class="mb-2">يجب أن يحتوي ملف Excel على الأعمدة التالية في الصف الأول:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>أعمدة مطلوبة:</strong>
                                        <ul class="mb-0">
                                            <li><code>name</code> - اسم المنتج</li>
                                            <li><code>price</code> - السعر</li>
                                            <li><code>quantity</code> - الكمية</li>
                                            <li><code>sku</code> - <span class="text-danger fw-bold">رمز المنتج (إجباري)</span></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>أعمدة اختيارية:</strong>
                                        <ul class="mb-0">
                                            <li><code>description</code> - الوصف</li>
                                            <li><code>category</code> - الفئة</li>
                                            <li><code>status</code> - الحالة (active/inactive)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- معاينة مرئية لتنسيق الملف -->
                            <div class="alert alert-light border">
                                <h6><i class="fas fa-eye me-2"></i>مثال على تنسيق الملف:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>name</th>
                                                <th>description</th>
                                                <th>price</th>
                                                <th>quantity</th>
                                                <th>category</th>
                                                <th>sku</th>
                                                <th>status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="table-success">
                                                <td>لابتوب Dell</td>
                                                <td>لابتوب عالي الأداء</td>
                                                <td>2500.00</td>
                                                <td>10</td>
                                                <td>إلكترونيات</td>
                                                <td>DELL-001</td>
                                                <td>active</td>
                                            </tr>
                                            <tr class="table-success">
                                                <td>هاتف Samsung</td>
                                                <td>هاتف ذكي متطور</td>
                                                <td>3200.00</td>
                                                <td>25</td>
                                                <td>إلكترونيات</td>
                                                <td>SAM-001</td>
                                                <td>active</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الصف الأول يحتوي على أسماء الأعمدة، والصفوف التالية تحتوي على البيانات
                                </small>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>معاينة البيانات
                                </button>
                                <a href="download-simple-excel-template.php" class="btn btn-outline-info">
                                    <i class="fas fa-download me-2"></i>تحميل نموذج Excel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>نصائح مهمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>قبل الاستيراد:</h6>
                            <ul class="mb-0 small">
                                <li>تأكد من صحة تنسيق البيانات</li>
                                <li>استخدم النموذج المتوفر لضمان التوافق</li>
                                <li>تحقق من عدم تكرار أرقام المنتجات (SKU)</li>
                                <li>تأكد من أن الأسعار والكميات أرقام صحيحة</li>
                                <li>احفظ الملف بصيغة Excel (.xlsx) أو CSV (UTF-8)</li>
                                <li>احفظ نسخة احتياطية من بياناتك</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>ملاحظات:</h6>
                            <ul class="mb-0 small">
                                <li>سيتم تجاهل الصفوف التي تحتوي على أخطاء</li>
                                <li>يمكنك مراجعة البيانات قبل الاستيراد النهائي</li>
                                <li>المنتجات المكررة لن يتم استيرادها</li>
                                <li>الحد الأقصى 1000 منتج في الملف الواحد</li>
                                <li>يدعم ملفات Excel والترميز العربي بالكامل</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>القيم المقبولة:</h6>
                            <div class="small">
                                <strong>الحالة:</strong> active, inactive, نشط, غير نشط<br>
                                <strong>السعر:</strong> أرقام موجبة (مثل: 100, 150.50)<br>
                                <strong>الكمية:</strong> أرقام صحيحة موجبة<br>
                                <strong>رمز المنتج:</strong> نص فريد (مثل: PROD-001)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($preview_data)): ?>
        <!-- معاينة البيانات -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>معاينة البيانات
                    <span class="badge bg-primary ms-2"><?php echo count($preview_data); ?> منتج</span>
                </h5>
            </div>
            <div class="card-body">
                <?php
                $valid_count = count(array_filter($preview_data, function($p) { return $p['valid']; }));
                $invalid_count = count($preview_data) - $valid_count;
                ?>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <strong><?php echo $valid_count; ?></strong> منتج صحيح
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-danger">
                            <strong><?php echo $invalid_count; ?></strong> منتج يحتوي على أخطاء
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <strong><?php echo count($preview_data); ?></strong> إجمالي المنتجات
                        </div>
                    </div>
                </div>

                <?php if ($valid_count > 0): ?>
                    <div class="mb-3">
                        <form method="POST">
                            <input type="hidden" name="action" value="import">
                            <button type="submit" class="btn btn-success" onclick="return confirm('هل أنت متأكد من استيراد <?php echo $valid_count; ?> منتج؟')">
                                <i class="fas fa-check me-2"></i>استيراد المنتجات الصحيحة (<?php echo $valid_count; ?>)
                            </button>
                            <a href="import-products.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </form>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>الصف</th>
                                <th>اسم المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الفئة</th>
                                <th>رمز المنتج</th>
                                <th>الحالة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($preview_data as $product): ?>
                                <tr class="<?php echo $product['valid'] ? 'table-success' : 'table-danger'; ?>">
                                    <td><?php echo $product['row_number']; ?></td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                    <td><?php echo number_format($product['price'], 2); ?> ر.س</td>
                                    <td><?php echo number_format($product['quantity']); ?></td>
                                    <td><?php echo htmlspecialchars($product['category'] ?? '-'); ?></td>
                                    <td>
                                        <?php if ($product['sku_source'] === 'provided' && !empty($product['sku'])): ?>
                                            <!-- SKU مُقدم من المستخدم -->
                                            <span class="text-primary fw-bold"><?php echo htmlspecialchars($product['sku']); ?></span>
                                            <br><small class="text-muted"><i class="fas fa-user me-1"></i>مُقدم من المستخدم</small>

                                        <?php elseif ($product['sku_source'] === 'missing'): ?>
                                            <!-- SKU مفقود -->
                                            <span class="text-danger fw-bold">مفقود</span>
                                            <br><small class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>مطلوب إدخال SKU</small>

                                        <?php else: ?>
                                            <!-- حالة غير متوقعة -->
                                            <span class="text-muted">-</span>
                                            <br><small class="text-warning"><i class="fas fa-question me-1"></i>غير محدد</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo ($product['status'] ?? 'active') == 'active' ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo ($product['status'] ?? 'active') == 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($product['valid']): ?>
                                            <span class="badge bg-success">صحيح</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger" title="<?php echo implode(', ', $product['errors']); ?>">خطأ</span>
                                            <br><small class="text-danger"><?php echo implode('<br>', $product['errors']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($import_results)): ?>
        <!-- نتائج الاستيراد -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-check me-2"></i>نتائج الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>الصف</th>
                                <th>اسم المنتج</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_results as $result): ?>
                                <tr class="<?php
                                    echo $result['status'] == 'success' ? 'table-success' :
                                        ($result['status'] == 'error' ? 'table-danger' : 'table-warning');
                                ?>">
                                    <td><?php echo $result['row']; ?></td>
                                    <td><?php echo htmlspecialchars($result['name']); ?></td>
                                    <td>
                                        <span class="badge <?php
                                            echo $result['status'] == 'success' ? 'bg-success' :
                                                ($result['status'] == 'error' ? 'bg-danger' : 'bg-warning');
                                        ?>">
                                            <?php
                                            echo $result['status'] == 'success' ? 'نجح' :
                                                ($result['status'] == 'error' ? 'فشل' : 'تم التجاهل');
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-boxes me-2"></i>عرض المنتجات
                    </a>
                    <a href="import-products.php" class="btn btn-secondary">
                        <i class="fas fa-plus me-2"></i>استيراد ملف آخر
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// التحقق من صحة الملف قبل الرفع
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('excel_file');
    const file = fileInput.files[0];

    if (!file) {
        alert('يرجى اختيار ملف Excel');
        e.preventDefault();
        return;
    }

    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const isValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidExtension) {
        alert('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx, .xls) أو CSV (.csv)');
        e.preventDefault();
        return;
    }

    if (file.size > 5 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        e.preventDefault();
        return;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
