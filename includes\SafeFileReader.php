<?php

/**
 * مكتبة آمنة لقراءة ملفات Excel و CSV
 * تتجنب مشاكل الترميز والأخطاء الشائعة
 */
class SafeFileReader {
    
    private $file_path;
    private $original_filename;
    
    public function __construct($file_path, $original_filename = null) {
        $this->file_path = $file_path;
        $this->original_filename = $original_filename;
    }
    
    /**
     * قراءة الملف وإرجاع البيانات
     */
    public function readFile() {
        if (!file_exists($this->file_path)) {
            throw new Exception('الملف غير موجود');
        }

        if (!is_readable($this->file_path)) {
            throw new Exception('لا يمكن قراءة الملف');
        }

        $file_size = filesize($this->file_path);
        if ($file_size > 5 * 1024 * 1024) { // 5MB
            throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        }

        if ($file_size === 0) {
            throw new Exception('الملف فارغ');
        }

        // تحديد نوع الملف
        $file_type = $this->detectFileType();

        if ($file_type === 'excel') {
            if ($this->canReadExcel()) {
                try {
                    return $this->readExcelFile();
                } catch (Exception $e) {
                    // إذا فشلت قراءة Excel، نعرض رسالة واضحة
                    throw new Exception('
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>ملف Excel غير مدعوم حالياً</h6>
                            <p>الملف الذي رفعته هو ملف Excel حقيقي (.xlsx) ولكن النظام لا يستطيع قراءته مباشرة.</p>
                            <p><strong>الحلول المتاحة:</strong></p>
                            <ol>
                                <li><strong>الحل الأسرع:</strong> <a href="convert-excel-to-csv.php" class="btn btn-sm btn-success">تحويل Excel إلى CSV</a></li>
                                <li><strong>أو:</strong> افتح الملف في Excel واحفظه بصيغة CSV (UTF-8)</li>
                                <li><strong>أو:</strong> استخدم النموذج المتوفر من النظام</li>
                            </ol>
                            <p class="mb-0"><strong>ملاحظة:</strong> النموذج المتوفر من النظام يعمل بشكل مثالي مع جميع إصدارات Excel.</p>
                        </div>
                    ');
                }
            } else {
                throw new Exception('
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>مكتبة Excel غير متوفرة</h6>
                        <p>لقراءة ملفات Excel مباشرة، يرجى:</p>
                        <ol>
                            <li><strong>استخدام محول النظام:</strong> <a href="convert-excel-to-csv.php" class="btn btn-sm btn-success">تحويل Excel إلى CSV</a></li>
                            <li><strong>أو تحويل يدوي:</strong> فتح الملف في Excel → حفظ باسم → CSV (UTF-8)</li>
                            <li><strong>أو:</strong> استخدام النموذج المتوفر من النظام</li>
                        </ol>
                        <p class="mb-0">النموذج المتوفر من النظام يعمل مع جميع إصدارات Excel.</p>
                    </div>
                ');
            }
        } else {
            return $this->readCSVFile();
        }
    }
    
    /**
     * تحديد نوع الملف
     */
    private function detectFileType() {
        // التحقق من الاسم الأصلي أولاً
        if ($this->original_filename) {
            $extension = strtolower(pathinfo($this->original_filename, PATHINFO_EXTENSION));
            if (in_array($extension, ['xlsx', 'xls'])) {
                return 'excel';
            }
            if ($extension === 'csv') {
                return 'csv';
            }
        }

        // فحص محتوى الملف
        $content = file_get_contents($this->file_path, false, null, 0, 100);

        // ملفات Excel الحديثة (.xlsx) تبدأ بـ PK (ZIP signature)
        if (substr($content, 0, 2) === 'PK') {
            return 'excel';
        }

        // ملفات Excel القديمة (.xls) لها signature مختلف
        if (substr($content, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1") {
            return 'excel';
        }

        // ملفات Excel XML
        if (strpos($content, '<?xml') !== false &&
            (strpos($content, 'Workbook') !== false || strpos($content, 'mso-application') !== false)) {
            return 'excel';
        }

        // ملفات CSV (تحتوي على فواصل أو أقواس اقتباس)
        if (strpos($content, ',') !== false || strpos($content, '"') !== false || strpos($content, ';') !== false) {
            return 'csv';
        }

        // افتراضياً CSV
        return 'csv';
    }
    
    /**
     * التحقق من إمكانية قراءة Excel
     */
    private function canReadExcel() {
        return file_exists('vendor/autoload.php') && 
               class_exists('PhpOffice\PhpSpreadsheet\IOFactory');
    }
    
    /**
     * قراءة ملف Excel
     */
    private function readExcelFile() {
        try {
            require_once 'vendor/autoload.php';
            
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->file_path);
            $worksheet = $spreadsheet->getActiveSheet();
            
            $data = [];
            $highestRow = min($worksheet->getHighestRow(), 1000); // حد أقصى 1000 صف
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
            $highestColumnIndex = min($highestColumnIndex, 20); // حد أقصى 20 عمود
            
            for ($row = 1; $row <= $highestRow; $row++) {
                $rowData = [];
                $hasData = false;
                
                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    try {
                        $cell = $worksheet->getCellByColumnAndRow($col, $row);
                        $value = $cell->getValue();
                        
                        // تحويل إلى نص بأمان
                        if ($value === null) {
                            $value = '';
                        } else {
                            $value = (string)$value;
                        }
                        
                        if (!empty(trim($value))) {
                            $hasData = true;
                        }
                        
                        $rowData[] = $value;
                    } catch (Exception $e) {
                        $rowData[] = '';
                    }
                }
                
                if ($hasData) {
                    $data[] = $rowData;
                }
            }
            
            return $data;
            
        } catch (Exception $e) {
            // إذا فشلت قراءة Excel، نحاول CSV
            return $this->readCSVFile();
        }
    }
    
    /**
     * قراءة ملف CSV بأمان
     */
    private function readCSVFile() {
        $data = [];
        
        try {
            // قراءة المحتوى
            $content = file_get_contents($this->file_path);
            if ($content === false) {
                throw new Exception('فشل في قراءة الملف');
            }
            
            // إزالة BOM إذا وجد
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }
            
            // تحويل الترميز بأمان
            if (!$this->isValidUTF8($content)) {
                // محاولة تحويل من ISO-8859-1
                $converted = @iconv('ISO-8859-1', 'UTF-8//IGNORE', $content);
                if ($converted !== false) {
                    $content = $converted;
                }
            }
            
            // تقسيم إلى أسطر
            $lines = preg_split('/\r\n|\r|\n/', $content);

            // اكتشاف الفاصل المستخدم من السطر الأول
            $delimiter = $this->detectDelimiter($lines);
            error_log("DEBUG SafeFileReader: Detected delimiter: '$delimiter'");

            foreach ($lines as $line_num => $line) {
                if (empty(trim($line))) {
                    continue; // تجاهل الأسطر الفارغة
                }

                // تحليل CSV بأمان مع الفاصل المكتشف
                $row = $this->parseCSVLine($line, $delimiter);

                // Debug: Log first few rows
                if (count($data) < 3) {
                    error_log("DEBUG SafeFileReader: Line $line_num: '" . substr($line, 0, 100) . "'");
                    error_log("DEBUG SafeFileReader: Parsed row: " . print_r($row, true));
                }

                if (!empty($row) && !empty(array_filter($row, 'trim'))) {
                    $data[] = $row;
                }

                // حد أقصى 1000 صف
                if (count($data) >= 1000) {
                    break;
                }
            }
            
            return $data;
            
        } catch (Exception $e) {
            throw new Exception('فشل في قراءة ملف CSV: ' . $e->getMessage());
        }
    }
    
    /**
     * تحليل سطر CSV بأمان
     */
    private function parseCSVLine($line, $delimiter = ',') {
        $result = [];
        $current = '';
        $in_quotes = false;
        $length = strlen($line);

        for ($i = 0; $i < $length; $i++) {
            $char = $line[$i];

            if ($char === '"') {
                if ($in_quotes && $i + 1 < $length && $line[$i + 1] === '"') {
                    $current .= '"';
                    $i++; // تخطي الاقتباس المضاعف
                } else {
                    $in_quotes = !$in_quotes;
                }
            } elseif ($char === $delimiter && !$in_quotes) {
                $result[] = $current;
                $current = '';
            } else {
                $current .= $char;
            }
        }

        $result[] = $current; // إضافة العنصر الأخير

        return $result;
    }
    
    /**
     * اكتشاف الفاصل المستخدم في الملف
     */
    private function detectDelimiter($lines) {
        // الفواصل المحتملة بترتيب الأولوية
        $delimiters = [';', ',', '\t', '|'];
        $delimiter_counts = [];

        // البحث في أول 5 أسطر غير فارغة
        $sample_lines = [];
        foreach ($lines as $line) {
            if (!empty(trim($line))) {
                $sample_lines[] = $line;
                if (count($sample_lines) >= 5) break;
            }
        }

        if (empty($sample_lines)) {
            return ','; // افتراضي
        }

        // عد كل فاصل في الأسطر العينة
        foreach ($delimiters as $delimiter) {
            $delimiter_counts[$delimiter] = 0;
            foreach ($sample_lines as $line) {
                // تجاهل الفواصل داخل الاقتباسات
                $in_quotes = false;
                $count = 0;
                for ($i = 0; $i < strlen($line); $i++) {
                    if ($line[$i] === '"') {
                        $in_quotes = !$in_quotes;
                    } elseif ($line[$i] === $delimiter && !$in_quotes) {
                        $count++;
                    }
                }
                $delimiter_counts[$delimiter] += $count;
            }
        }

        // اختيار الفاصل الأكثر تكراراً
        $max_count = 0;
        $best_delimiter = ',';
        foreach ($delimiter_counts as $delimiter => $count) {
            if ($count > $max_count) {
                $max_count = $count;
                $best_delimiter = $delimiter;
            }
        }

        // تحويل \t إلى tab حقيقي
        if ($best_delimiter === '\t') {
            $best_delimiter = "\t";
        }

        return $best_delimiter;
    }

    /**
     * التحقق من صحة UTF-8
     */
    private function isValidUTF8($str) {
        return mb_check_encoding($str, 'UTF-8');
    }
    
    /**
     * التحقق من صحة الملف
     */
    public function validateFile() {
        if (!file_exists($this->file_path)) {
            throw new Exception('الملف غير موجود');
        }
        
        if (!is_readable($this->file_path)) {
            throw new Exception('لا يمكن قراءة الملف');
        }
        
        $file_size = filesize($this->file_path);
        if ($file_size > 5 * 1024 * 1024) {
            throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        }
        
        if ($file_size === 0) {
            throw new Exception('الملف فارغ');
        }
        
        return true;
    }
    
    /**
     * الحصول على معلومات الملف
     */
    public function getFileInfo() {
        return [
            'file_path' => $this->file_path,
            'original_filename' => $this->original_filename,
            'file_exists' => file_exists($this->file_path),
            'file_size' => file_exists($this->file_path) ? filesize($this->file_path) : 0,
            'is_readable' => is_readable($this->file_path),
            'detected_type' => $this->detectFileType(),
            'can_read_excel' => $this->canReadExcel()
        ];
    }
}
?>
