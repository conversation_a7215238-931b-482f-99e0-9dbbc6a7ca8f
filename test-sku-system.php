<?php
/**
 * اختبار شامل لنظام SKU المحسن
 */

require_once 'config/database.php';
require_once 'includes/auth.php';

// بدء الجلسة
session_start();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار نظام SKU المحسن</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo ".test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }";
echo ".test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }";
echo ".test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }";
echo ".test-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }";
echo ".test-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-vial me-2'></i>اختبار نظام SKU المحسن</h1>";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    echo "<div class='test-result test-success'><i class='fas fa-check me-2'></i>تم الاتصال بقاعدة البيانات بنجاح</div>";
} catch (Exception $e) {
    echo "<div class='test-result test-error'><i class='fas fa-times me-2'></i>خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// تضمين الدوال من ملف الاستيراد
include_once 'import-products.php';

// اختبار 1: دالة إنشاء SKU الأساسي
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-code me-2'></i>اختبار 1: دالة إنشاء SKU الأساسي</h3></div>";
echo "<div class='card-body'>";

$test_names = [
    'منتج تجريبي',
    'Product Test',
    'كمبيوتر محمول',
    'Laptop Computer',
    'هاتف ذكي جديد',
    'Smart Phone New',
    'طاولة خشبية',
    'Wooden Table',
    '123 منتج رقمي',
    'Digital Product 456'
];

foreach ($test_names as $name) {
    $base_sku = generateSKUBase($name);
    echo "<div class='test-result test-info'>";
    echo "<strong>الاسم:</strong> " . htmlspecialchars($name) . " → <strong>SKU الأساسي:</strong> $base_sku";
    echo "</div>";
}

echo "</div></div>";

// اختبار 2: دالة إنشاء SKU فريد
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-fingerprint me-2'></i>اختبار 2: دالة إنشاء SKU فريد</h3></div>";
echo "<div class='card-body'>";

// الحصول على مستخدم للاختبار
$stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
$user = $stmt->fetch();

if (!$user) {
    echo "<div class='test-result test-error'><i class='fas fa-user-times me-2'></i>لا يوجد مستخدمين نشطين للاختبار</div>";
} else {
    $test_user_id = $user['id'];
    echo "<div class='test-result test-info'><i class='fas fa-user me-2'></i>اختبار مع المستخدم ID: $test_user_id</div>";
    
    foreach (array_slice($test_names, 0, 5) as $name) {
        $unique_sku = generateUniqueSKU($pdo, $test_user_id, $name);
        echo "<div class='test-result test-success'>";
        echo "<strong>الاسم:</strong> " . htmlspecialchars($name) . " → <strong>SKU فريد:</strong> $unique_sku";
        echo "</div>";
    }
}

echo "</div></div>";

// اختبار 3: التحقق من تفرد SKU
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-shield-alt me-2'></i>اختبار 3: التحقق من تفرد SKU</h3></div>";
echo "<div class='card-body'>";

if ($user) {
    // إنشاء عدة SKU للمنتج نفسه
    $test_product_name = 'منتج اختبار التفرد';
    $generated_skus = [];
    
    for ($i = 1; $i <= 10; $i++) {
        $sku = generateUniqueSKU($pdo, $test_user_id, $test_product_name);
        $generated_skus[] = $sku;
    }
    
    // التحقق من عدم وجود تكرار
    $unique_skus = array_unique($generated_skus);
    
    if (count($unique_skus) === count($generated_skus)) {
        echo "<div class='test-result test-success'>";
        echo "<i class='fas fa-check-double me-2'></i>تم إنشاء " . count($generated_skus) . " SKU فريدة بنجاح";
        echo "<br><small>الأكواد: " . implode(', ', $generated_skus) . "</small>";
        echo "</div>";
    } else {
        echo "<div class='test-result test-error'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>تم العثور على تكرار في الأكواد المُولدة!";
        echo "<br><small>المُولد: " . count($generated_skus) . " | الفريد: " . count($unique_skus) . "</small>";
        echo "</div>";
    }
}

echo "</div></div>";

// اختبار 4: دالة تحديد SKU النهائي
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-decision me-2'></i>اختبار 4: دالة تحديد SKU النهائي</h3></div>";
echo "<div class='card-body'>";

if ($user) {
    // اختبار حالات مختلفة
    $test_cases = [
        [
            'name' => 'منتج مع SKU مُقدم',
            'product' => [
                'name' => 'منتج اختبار 1',
                'sku_source' => 'provided',
                'sku' => 'USER-SKU-001'
            ]
        ],
        [
            'name' => 'منتج مع SKU معاينة',
            'product' => [
                'name' => 'منتج اختبار 2',
                'sku_source' => 'auto',
                'preview_sku' => 'PREV-SKU-002'
            ]
        ],
        [
            'name' => 'منتج بدون SKU',
            'product' => [
                'name' => 'منتج اختبار 3'
            ]
        ]
    ];
    
    foreach ($test_cases as $case) {
        $final_sku = determineFinalSKU($pdo, $test_user_id, $case['product']);
        echo "<div class='test-result test-info'>";
        echo "<strong>" . $case['name'] . ":</strong> $final_sku";
        echo "</div>";
    }
}

echo "</div></div>";

// اختبار 5: دالة التحقق من وجود SKU
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-search me-2'></i>اختبار 5: دالة التحقق من وجود SKU</h3></div>";
echo "<div class='card-body'>";

if ($user) {
    // اختبار SKU موجود وغير موجود
    $test_skus = ['EXISTING-SKU', 'NON-EXISTING-SKU-' . time()];
    
    foreach ($test_skus as $test_sku) {
        $exists = checkSKUExists($pdo, $test_user_id, $test_sku);
        $status_class = $exists ? 'test-warning' : 'test-success';
        $status_text = $exists ? 'موجود' : 'غير موجود';
        $icon = $exists ? 'fas fa-exclamation-triangle' : 'fas fa-check';
        
        echo "<div class='test-result $status_class'>";
        echo "<i class='$icon me-2'></i><strong>SKU:</strong> $test_sku → $status_text";
        echo "</div>";
    }
}

echo "</div></div>";

// اختبار 6: دالة تحويل الأحرف العربية
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h3><i class='fas fa-language me-2'></i>اختبار 6: تحويل الأحرف العربية</h3></div>";
echo "<div class='card-body'>";

$arabic_chars = ['ا', 'ب', 'ت', 'ج', 'د', 'ر', 'س', 'ع', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];

foreach ($arabic_chars as $char) {
    $transliterated = transliterateArabicChar($char);
    echo "<span class='badge bg-primary me-2 mb-2'>$char → $transliterated</span>";
}

echo "</div></div>";

// ملخص النتائج
echo "<div class='card mt-4 border-success'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3 class='mb-0'><i class='fas fa-trophy me-2'></i>ملخص الاختبارات</h3>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5><i class='fas fa-check-circle text-success me-2'></i>المميزات المُختبرة:</h5>";
echo "<ul class='list-unstyled'>";
echo "<li><i class='fas fa-check text-success me-2'></i>إنشاء SKU من النصوص العربية والإنجليزية</li>";
echo "<li><i class='fas fa-check text-success me-2'></i>ضمان تفرد SKU على مستوى المستخدم</li>";
echo "<li><i class='fas fa-check text-success me-2'></i>معالجة ذكية للحالات المختلفة</li>";
echo "<li><i class='fas fa-check text-success me-2'></i>تحويل الأحرف العربية إلى إنجليزية</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5><i class='fas fa-cogs text-primary me-2'></i>الوظائف المُحسنة:</h5>";
echo "<ul class='list-unstyled'>";
echo "<li><i class='fas fa-arrow-right text-primary me-2'></i>generateSKUBase()</li>";
echo "<li><i class='fas fa-arrow-right text-primary me-2'></i>generateUniqueSKU()</li>";
echo "<li><i class='fas fa-arrow-right text-primary me-2'></i>determineFinalSKU()</li>";
echo "<li><i class='fas fa-arrow-right text-primary me-2'></i>checkSKUExists()</li>";
echo "<li><i class='fas fa-arrow-right text-primary me-2'></i>transliterateArabicChar()</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4 mb-4'>";
echo "<a href='import-products.php' class='btn btn-primary btn-lg me-3'>";
echo "<i class='fas fa-upload me-2'></i>جرب استيراد المنتجات";
echo "</a>";
echo "<a href='debug-products-import.php' class='btn btn-info btn-lg'>";
echo "<i class='fas fa-bug me-2'></i>تشخيص النظام";
echo "</a>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
