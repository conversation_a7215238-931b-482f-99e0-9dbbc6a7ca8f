<?php
require_once 'includes/SafeFileReader.php';

echo "<h1>🔍 Step-by-Step Import Debug</h1>";

// Test with the actual sample file
$test_file = 'sample-products-test.csv';

if (!file_exists($test_file)) {
    echo "<div style='color: red;'>❌ File not found: $test_file</div>";
    exit;
}

echo "<h2>Step 1: Raw File Analysis</h2>";

$raw_content = file_get_contents($test_file);
echo "<h3>File size: " . strlen($raw_content) . " bytes</h3>";
echo "<h3>First 500 characters:</h3>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>" . htmlspecialchars(substr($raw_content, 0, 500)) . "</pre>";

// Check for BOM
if (substr($raw_content, 0, 3) === "\xEF\xBB\xBF") {
    echo "<div style='color: orange;'>⚠️ UTF-8 BOM detected</div>";
} else {
    echo "<div style='color: green;'>✅ No BOM detected</div>";
}

echo "<h2>Step 2: Line Analysis</h2>";

$lines = preg_split('/\r\n|\r|\n/', $raw_content);
echo "<div>Total lines: " . count($lines) . "</div>";

echo "<h3>First 3 lines:</h3>";
for ($i = 0; $i < min(3, count($lines)); $i++) {
    echo "<div>Line $i: '" . htmlspecialchars($lines[$i]) . "' (length: " . strlen($lines[$i]) . ")</div>";
}

echo "<h2>Step 3: Delimiter Detection</h2>";

// Manual delimiter detection
$delimiters = [';', ',', '\t', '|'];
$delimiter_counts = [];

foreach ($delimiters as $delimiter) {
    $count = 0;
    if (isset($lines[0])) {
        $count = substr_count($lines[0], $delimiter);
    }
    $delimiter_counts[$delimiter] = $count;
    echo "<div>Delimiter '$delimiter': $count occurrences</div>";
}

$best_delimiter = array_keys($delimiter_counts, max($delimiter_counts))[0];
echo "<div style='color: green;'>✅ Best delimiter: '$best_delimiter'</div>";

echo "<h2>Step 4: Manual Parsing</h2>";

if (count($lines) > 0) {
    $header_line = $lines[0];
    $headers = explode($best_delimiter, $header_line);
    
    echo "<h3>Headers:</h3>";
    foreach ($headers as $i => $header) {
        $cleaned = trim($header);
        echo "<div>[$i] Raw: '" . htmlspecialchars($header) . "' | Cleaned: '" . htmlspecialchars($cleaned) . "'</div>";
    }
    
    // Find SKU column
    $sku_position = -1;
    foreach ($headers as $i => $header) {
        if (strtolower(trim($header)) === 'sku') {
            $sku_position = $i;
            break;
        }
    }
    
    echo "<div style='color: " . ($sku_position >= 0 ? 'green' : 'red') . ";'>";
    echo ($sku_position >= 0 ? "✅ SKU column found at position $sku_position" : "❌ SKU column not found");
    echo "</div>";
    
    if (count($lines) > 1 && $sku_position >= 0) {
        echo "<h3>First Product Data:</h3>";
        $data_line = $lines[1];
        $data_parts = explode($best_delimiter, $data_line);
        
        echo "<div>Data line: '" . htmlspecialchars($data_line) . "'</div>";
        echo "<div>Parts count: " . count($data_parts) . "</div>";
        
        foreach ($data_parts as $i => $part) {
            $style = ($i === $sku_position) ? "background: #fff3cd; font-weight: bold;" : "";
            echo "<div style='$style'>[$i] '" . htmlspecialchars($part) . "'</div>";
        }
        
        if (isset($data_parts[$sku_position])) {
            $sku_value = $data_parts[$sku_position];
            echo "<h4>SKU Analysis:</h4>";
            echo "<div>Raw SKU: '" . htmlspecialchars($sku_value) . "'</div>";
            echo "<div>Trimmed SKU: '" . htmlspecialchars(trim($sku_value)) . "'</div>";
            echo "<div>Length: " . strlen(trim($sku_value)) . "</div>";
            echo "<div>Is empty: " . (empty(trim($sku_value)) ? 'YES ❌' : 'NO ✅') . "</div>";
            
            // Character analysis
            if (!empty(trim($sku_value))) {
                echo "<div>Character codes: ";
                $trimmed = trim($sku_value);
                for ($j = 0; $j < strlen($trimmed); $j++) {
                    echo ord($trimmed[$j]) . " ";
                }
                echo "</div>";
            }
        }
    }
}

echo "<h2>Step 5: SafeFileReader Test</h2>";

try {
    $reader = new SafeFileReader($test_file, $test_file);
    $rows = $reader->readFile();
    
    echo "<div style='color: green;'>✅ SafeFileReader successful</div>";
    echo "<div>Rows returned: " . count($rows) . "</div>";
    
    if (count($rows) > 0) {
        echo "<h3>SafeFileReader Headers:</h3>";
        foreach ($rows[0] as $i => $header) {
            echo "<div>[$i] '" . htmlspecialchars($header) . "'</div>";
        }
        
        if (count($rows) > 1) {
            echo "<h3>SafeFileReader First Product:</h3>";
            foreach ($rows[1] as $i => $value) {
                echo "<div>[$i] '" . htmlspecialchars($value) . "'</div>";
            }
            
            // Compare with manual parsing
            $manual_sku = isset($data_parts[$sku_position]) ? trim($data_parts[$sku_position]) : '';
            $safereader_sku = isset($rows[1][$sku_position]) ? trim($rows[1][$sku_position]) : '';
            
            echo "<h3>SKU Comparison:</h3>";
            echo "<div>Manual parsing SKU: '" . htmlspecialchars($manual_sku) . "'</div>";
            echo "<div>SafeFileReader SKU: '" . htmlspecialchars($safereader_sku) . "'</div>";
            echo "<div>Match: " . ($manual_sku === $safereader_sku ? 'YES ✅' : 'NO ❌') . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ SafeFileReader error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "<h2>Step 6: Import Logic Simulation</h2>";

if (isset($rows) && count($rows) > 1) {
    // Simulate the exact import logic
    $headers = array_map(function($header) {
        $cleaned = trim($header);
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = strtolower($cleaned);
        return $cleaned;
    }, $rows[0]);
    
    echo "<h3>Processed Headers:</h3>";
    foreach ($headers as $i => $header) {
        echo "<div>[$i] '" . htmlspecialchars($header) . "'</div>";
    }
    
    // Header mapping
    $header_map = [
        'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code']
    ];
    
    $found_headers = [];
    foreach ($header_map as $standard_name => $alternatives) {
        foreach ($alternatives as $alt) {
            $alt_cleaned = strtolower(trim($alt));
            $position = array_search($alt_cleaned, $headers);
            if ($position !== false) {
                $found_headers[$standard_name] = $position;
                echo "<div style='color: green;'>✅ Found '$standard_name' at position $position</div>";
                break;
            }
        }
    }
    
    if (isset($found_headers['sku'])) {
        $sku_pos = $found_headers['sku'];
        $row = $rows[1];
        $value = isset($row[$sku_pos]) ? trim($row[$sku_pos]) : '';
        
        echo "<h3>Final SKU Processing:</h3>";
        echo "<div>Position: $sku_pos</div>";
        echo "<div>Raw value: '" . htmlspecialchars($row[$sku_pos] ?? 'NULL') . "'</div>";
        echo "<div>Trimmed value: '" . htmlspecialchars($value) . "'</div>";
        
        $cleaned_sku = trim($value);
        $final_sku = !empty($cleaned_sku) ? $cleaned_sku : '';
        
        echo "<div>Final SKU: '" . htmlspecialchars($final_sku) . "'</div>";
        echo "<div>Would pass validation: " . (!empty($final_sku) ? 'YES ✅' : 'NO ❌') . "</div>";
    }
}
?>
