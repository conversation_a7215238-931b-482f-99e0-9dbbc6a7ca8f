<?php
/**
 * Admin Footer Template
 * Multi-Tenant Administrative Management System
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Access denied');
}
?>
    </div> <!-- End Main Content -->
    
    <!-- Footer -->
    <footer class="footer mt-5 py-4 bg-white border-top">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="text-muted">
                        <strong><?php echo ADMIN_NAME; ?></strong> - الإصدار <?php echo ADMIN_VERSION; ?>
                    </div>
                    <div class="text-muted small">
                        © <?php echo date('Y'); ?> جميع الحقوق محفوظة
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="text-muted small">
                        <i class="fas fa-server me-1"></i>
                        حالة النظام: <span class="text-success">متصل</span>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        آخر تحديث: <?php echo date('Y/m/d H:i:s'); ?>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JavaScript -->
    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        });
        
        // Mobile Sidebar Toggle
        if (window.innerWidth <= 768) {
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('show');
            });
        }
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Confirm delete actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
                if (!confirm('هل أنت متأكد من هذا الإجراء؟ لا يمكن التراجع عنه.')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
        
        // AJAX Form Handler
        function submitAjaxForm(form, callback) {
            const formData = new FormData(form);
            
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (callback) {
                    callback(data);
                } else {
                    if (data.success) {
                        showAlert('تم الحفظ بنجاح', 'success');
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location.href = data.redirect;
                            }, 1000);
                        }
                    } else {
                        showAlert(data.message || 'حدث خطأ غير متوقع', 'danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ في الاتصال', 'danger');
            });
        }
        
        // Show Alert Function
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.appendChild(alertDiv);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    const bsAlert = new bootstrap.Alert(alertDiv);
                    bsAlert.close();
                }
            }, 5000);
        }
        
        // Create Alert Container
        function createAlertContainer() {
            const container = document.createElement('div');
            container.id = 'alertContainer';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }
        
        // Data Tables Initialization
        function initDataTable(tableId, options = {}) {
            const defaultOptions = {
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>'
            };
            
            const finalOptions = Object.assign(defaultOptions, options);
            
            if (typeof $ !== 'undefined' && $.fn.DataTable) {
                return $('#' + tableId).DataTable(finalOptions);
            }
        }
        
        // Format Numbers for Arabic
        function formatArabicNumber(number, decimals = 0) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            }).format(number);
        }
        
        // Format Currency
        function formatCurrency(amount, currency = 'SAR') {
            return new Intl.NumberFormat('ar-SA', {
                style: 'currency',
                currency: currency,
                currencyDisplay: 'symbol'
            }).format(amount);
        }
        
        // Session Timeout Warning
        let sessionTimeout;
        let warningShown = false;
        
        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            warningShown = false;
            
            // Warning 5 minutes before timeout
            sessionTimeout = setTimeout(() => {
                if (!warningShown) {
                    warningShown = true;
                    if (confirm('ستنتهي جلستك خلال 5 دقائق. هل تريد تجديدها؟')) {
                        // Ping server to refresh session
                        fetch('../api/v1/auth/refresh', {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        resetSessionTimeout();
                    }
                }
            }, (<?php echo ADMIN_SESSION_TIMEOUT; ?> - 300) * 1000); // 5 minutes before timeout
        }
        
        // Initialize session timeout
        resetSessionTimeout();
        
        // Reset timeout on user activity
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, resetSessionTimeout, true);
        });
        
        // Real-time notifications (if WebSocket is available)
        function initNotifications() {
            // This would connect to a WebSocket server for real-time notifications
            // For now, we'll poll for notifications every 30 seconds
            setInterval(() => {
                fetch('../api/v1/notifications/unread', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.count > 0) {
                        const badge = document.querySelector('.notification-badge');
                        if (badge) {
                            badge.textContent = data.count;
                            badge.style.display = 'flex';
                        }
                    }
                })
                .catch(error => {
                    console.log('Notification check failed:', error);
                });
            }, 30000);
        }
        
        // Initialize notifications
        initNotifications();
        
        // Print functionality
        function printPage() {
            window.print();
        }
        
        // Export functionality
        function exportData(format, url) {
            const link = document.createElement('a');
            link.href = url + '?format=' + format;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
    
    <!-- Page-specific scripts can be added here -->
    <?php if (isset($additionalScripts)): ?>
        <?php echo $additionalScripts; ?>
    <?php endif; ?>
</body>
</html>
