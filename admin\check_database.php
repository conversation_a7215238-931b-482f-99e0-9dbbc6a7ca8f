<?php
/**
 * Check Database State
 * This script checks the current state of the database
 */

// Include the main database configuration
require_once __DIR__ . '/../config/database.php';

try {
    // Create Database instance
    $db = new Database();
    $pdo = $db->connect();
    
    echo "<h2>Database Connection: ✅ Success</h2>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $usersTableExists = $stmt->rowCount() > 0;
    
    echo "<h3>Main Tables Status:</h3>";
    echo "<p>Users table exists: " . ($usersTableExists ? "✅ Yes" : "❌ No") . "</p>";
    
    if ($usersTableExists) {
        // Check users table structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Users Table Structure:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasEmailVerified = false;
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'email_verified') {
                $hasEmailVerified = true;
            }
        }
        echo "</table>";
        
        echo "<p>Has email_verified column: " . ($hasEmailVerified ? "✅ Yes" : "❌ No") . "</p>";
        
        // Count users
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        echo "<p>Total users: {$userCount}</p>";
    }
    
    // Check admin tables
    $adminTables = ['admin_users', 'admin_sessions', 'admin_logs', 'admin_api_keys', 'admin_settings', 'admin_notifications', 'admin_stats_cache'];
    
    echo "<h3>Admin Tables Status:</h3>";
    foreach ($adminTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->rowCount() > 0;
        echo "<p>{$table}: " . ($exists ? "✅ Exists" : "❌ Missing") . "</p>";
    }
    
    // Check other main tables
    $mainTables = ['clients', 'invoices', 'invoice_items', 'products'];
    
    echo "<h3>Main System Tables Status:</h3>";
    foreach ($mainTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->rowCount() > 0;
        echo "<p>{$table}: " . ($exists ? "✅ Exists" : "❌ Missing") . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error: " . htmlspecialchars($e->getMessage()) . "</h2>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
