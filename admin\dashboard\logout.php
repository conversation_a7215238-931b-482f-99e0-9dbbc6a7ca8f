<?php
/**
 * Admin Logout
 * Multi-Tenant Administrative Management System
 */

define('ADMIN_SYSTEM', true);
require_once '../config/admin_config.php';

// Check if admin is logged in
if (isAdminLoggedIn()) {
    $admin = getCurrentAdmin();
    
    try {
        // Log logout activity
        logAdminActivity('logout', 'admin_users', $admin['id'], 
            "تسجيل خروج المستخدم: {$admin['username']}");
        
        // Clear session data
        destroyAdminSession();
        
        // Clear remember me cookie if exists
        if (isset($_COOKIE['admin_remember'])) {
            setcookie('admin_remember', '', time() - 3600, '/', '', true, true);
        }
        
    } catch (Exception $e) {
        logAdminError("Logout error: " . $e->getMessage());
    }
}

// Destroy the entire session
session_destroy();

// Redirect to login page
header('Location: login.php?message=logged_out');
exit;
?>
