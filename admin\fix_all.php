<?php
/**
 * Complete Fix for Admin System
 * This script fixes all common issues and ensures the admin system works
 */

echo "<h2>🔧 إصلاح شامل للنظام الإداري</h2>";

$errors = [];
$success = [];

try {
    // Step 1: Create installation markers
    $markerFile1 = __DIR__ . '/admin_installed.txt';
    $markerFile2 = __DIR__ . '/.installed';
    
    $timestamp = date('Y-m-d H:i:s');
    
    file_put_contents($markerFile1, $timestamp . " - تم تثبيت النظام الإداري بنجاح\n");
    file_put_contents($markerFile2, $timestamp . " - Admin system installed successfully\n");
    
    $success[] = "تم إنشاء ملفات التثبيت";
    
    // Step 2: Test database connection
    require_once __DIR__ . '/../config/database.php';
    $db = new Database();
    $pdo = $db->connect();
    
    $success[] = "تم الاتصال بقاعدة البيانات بنجاح";
    
    // Step 3: Ensure admin_users table exists
    $createAdminUsersTable = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator', 'viewer') DEFAULT 'admin',
        permissions JSON,
        is_active BOOLEAN DEFAULT TRUE,
        avatar VARCHAR(255) NULL,
        phone VARCHAR(20) NULL,
        two_factor_enabled BOOLEAN DEFAULT FALSE,
        two_factor_secret VARCHAR(32) NULL,
        email_verified BOOLEAN DEFAULT FALSE,
        email_verification_token VARCHAR(64) NULL,
        password_reset_token VARCHAR(64) NULL,
        password_reset_expires TIMESTAMP NULL,
        last_login TIMESTAMP NULL,
        last_login_ip VARCHAR(45) NULL,
        login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createAdminUsersTable);
    $success[] = "تم التأكد من وجود جدول admin_users";
    
    // Step 4: Create admin_sessions table
    $createAdminSessionsTable = "
    CREATE TABLE IF NOT EXISTS admin_sessions (
        id VARCHAR(128) PRIMARY KEY,
        admin_id INT NOT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE CASCADE,
        INDEX idx_admin_id (admin_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_last_activity (last_activity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createAdminSessionsTable);
    $success[] = "تم التأكد من وجود جدول admin_sessions";
    
    // Step 5: Check if default admin exists, if not create it
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn();
    
    if (!$adminExists) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (
                username, email, password, first_name, last_name, role, 
                permissions, is_active, email_verified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $permissions = json_encode([
            'users' => ['view', 'create', 'edit', 'delete'],
            'invoices' => ['view', 'create', 'edit', 'delete'],
            'clients' => ['view', 'create', 'edit', 'delete'],
            'products' => ['view', 'create', 'edit', 'delete'],
            'reports' => ['view', 'export'],
            'settings' => ['view', 'edit'],
            'logs' => ['view'],
            'system' => ['view', 'edit']
        ]);
        
        $stmt->execute([
            'admin',
            '<EMAIL>',
            $hashedPassword,
            'مدير',
            'النظام',
            'super_admin',
            $permissions,
            1,
            1
        ]);
        
        $success[] = "تم إنشاء المستخدم الإداري الافتراضي";
    } else {
        $success[] = "المستخدم الإداري موجود مسبقاً";
    }
    
    // Step 6: Test admin config
    if (file_exists(__DIR__ . '/config/admin_config.php')) {
        $success[] = "ملف admin_config.php موجود";
    } else {
        $errors[] = "ملف admin_config.php غير موجود";
    }
    
    // Step 7: Test login page
    if (file_exists(__DIR__ . '/dashboard/login.php')) {
        $success[] = "صفحة تسجيل الدخول موجودة";
    } else {
        $errors[] = "صفحة تسجيل الدخول غير موجودة";
    }
    
} catch (Exception $e) {
    $errors[] = "خطأ: " . $e->getMessage();
}

// Display results
if (!empty($success)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ تم بنجاح:</h3>";
    echo "<ul>";
    foreach ($success as $msg) {
        echo "<li>{$msg}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ أخطاء:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (empty($errors)) {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
    echo "<p><a href='dashboard/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام الإداري</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 بيانات تسجيل الدخول</h3>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
    direction: rtl;
}
h2, h3 { color: #333; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
