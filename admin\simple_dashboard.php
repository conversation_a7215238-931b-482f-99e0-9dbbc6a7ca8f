<?php
/**
 * Simple Admin Dashboard
 * Standalone dashboard without complex dependencies
 */

session_start();

// Check if logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: simple_login.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple_login.php');
    exit;
}

try {
    // Include database configuration
    require_once __DIR__ . '/../config/database.php';
    
    // Create database connection
    $db = new Database();
    $pdo = $db->connect();
    
    // Get statistics
    $stats = [];
    
    // Users statistics
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE is_active = 1");
    $stats['active_users'] = $stmt->fetchColumn();
    
    // Invoices statistics
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices");
    $stats['total_invoices'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT SUM(total_amount) as revenue FROM invoices WHERE status = 'paid'");
    $stats['total_revenue'] = $stmt->fetchColumn() ?: 0;
    
    // Clients statistics
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM clients");
    $stats['total_clients'] = $stmt->fetchColumn();
    
    // Recent users
    $stmt = $pdo->query("SELECT id, username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    $recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent invoices
    $stmt = $pdo->query("
        SELECT i.id, i.invoice_number, i.total_amount, i.status, i.created_at, u.username 
        FROM invoices i 
        JOIN users u ON i.user_id = u.id 
        ORDER BY i.created_at DESC LIMIT 5
    ");
    $recent_invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - النظام الإداري</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .bg-primary-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .bg-success-gradient { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .bg-warning-gradient { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .bg-info-gradient { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>النظام الإداري
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($_SESSION['admin_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?logout=1">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Welcome Message -->
        <div class="row mb-4">
            <div class="col-12">
                <h2>مرحباً، <?php echo htmlspecialchars($_SESSION['admin_name']); ?>!</h2>
                <p class="text-muted">إليك نظرة عامة على نشاط منصة الفواتير</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-primary-gradient me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo number_format($stats['total_users']); ?></h5>
                            <small class="text-muted">إجمالي المستخدمين</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-success-gradient me-3">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo number_format($stats['total_invoices']); ?></h5>
                            <small class="text-muted">إجمالي الفواتير</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-warning-gradient me-3">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo number_format($stats['total_revenue'], 2); ?> ر.س</h5>
                            <small class="text-muted">إجمالي الإيرادات</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body d-flex align-items-center">
                        <div class="stats-icon bg-info-gradient me-3">
                            <i class="fas fa-address-book"></i>
                        </div>
                        <div>
                            <h5 class="card-title mb-0"><?php echo number_format($stats['total_clients']); ?></h5>
                            <small class="text-muted">إجمالي العملاء</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>المستخدمون الجدد</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_users)): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_users as $user): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">لا توجد بيانات</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file-invoice me-2"></i>الفواتير الحديثة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recent_invoices)): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong><br>
                                            <small class="text-muted">بواسطة: <?php echo htmlspecialchars($invoice['username']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <strong><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</strong><br>
                                            <span class="badge bg-<?php echo $invoice['status'] === 'paid' ? 'success' : 'warning'; ?>">
                                                <?php echo $invoice['status']; ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">لا توجد بيانات</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
