<?php
/**
 * Test Admin Login System
 * Quick test to verify admin system is working
 */

// Check if admin system is installed
if (!file_exists(__DIR__ . '/admin_installed.txt')) {
    die('<h2>❌ النظام الإداري غير مثبت</h2><p><a href="web_install.php">اضغط هنا للتثبيت</a></p>');
}

try {
    // Include the main database configuration
    require_once __DIR__ . '/../config/database.php';
    
    // Create Database instance
    $db = new Database();
    $pdo = $db->connect();
    
    echo "<h2>✅ اختبار النظام الإداري</h2>";
    
    // Check admin_users table
    $stmt = $pdo->query("SELECT COUNT(*) FROM admin_users");
    $adminCount = $stmt->fetchColumn();
    echo "<p>عدد المديرين: {$adminCount}</p>";
    
    // Check if default admin exists
    $stmt = $pdo->prepare("SELECT username, email, role FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ المستخدم الإداري الافتراضي موجود</h3>";
        echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($admin['username']) . "</p>";
        echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($admin['email']) . "</p>";
        echo "<p><strong>الدور:</strong> " . htmlspecialchars($admin['role']) . "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ المستخدم الإداري الافتراضي غير موجود</h3>";
        echo "</div>";
    }
    
    // Check main tables
    $tables = ['users', 'clients', 'invoices', 'admin_users', 'admin_sessions', 'admin_logs', 'admin_settings'];
    echo "<h3>حالة الجداول:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "<li>✅ {$table}: {$count} سجل</li>";
        } catch (Exception $e) {
            echo "<li>❌ {$table}: غير موجود</li>";
        }
    }
    echo "</ul>";
    
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔗 روابط النظام</h3>";
    echo "<p><a href='dashboard/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>صفحة تسجيل الدخول</a></p>";
    echo "<p><a href='dashboard/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📋 بيانات تسجيل الدخول</h3>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> <EMAIL></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</h2>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>

<style>
body { 
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
    direction: rtl;
}
h2, h3 { color: #333; }
a { color: #007bff; }
</style>
