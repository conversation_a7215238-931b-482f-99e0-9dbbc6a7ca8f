<?php
/**
 * Admin System Permissions Configuration
 * Defines all permission levels and categories for the admin system
 */

if (!defined('ADMIN_SYSTEM')) {
    die('Direct access not allowed');
}

/**
 * Permission Levels
 */
define('PERMISSION_LEVELS', [
    'super_admin' => [
        'name' => 'مدير عام',
        'description' => 'صلاحية كاملة على النظام وإدارة المديرين الآخرين',
        'level' => 100
    ],
    'admin' => [
        'name' => 'مدير',
        'description' => 'صلاحية كاملة على بيانات المستخدمين وإعدادات النظام',
        'level' => 80
    ],
    'moderator' => [
        'name' => 'مشرف',
        'description' => 'صلاحية محدودة على بيانات المستخدمين وقراءة إعدادات النظام',
        'level' => 60
    ],
    'viewer' => [
        'name' => 'مراقب',
        'description' => 'صلاحية قراءة فقط',
        'level' => 40
    ]
]);

/**
 * Permission Categories and Actions
 */
define('PERMISSION_CATEGORIES', [
    'users' => [
        'name' => 'إدارة المستخدمين',
        'actions' => [
            'view' => 'عرض المستخدمين',
            'create' => 'إنشاء مستخدم جديد',
            'edit' => 'تعديل بيانات المستخدم',
            'delete' => 'حذف المستخدم',
            'suspend' => 'تعليق المستخدم',
            'activate' => 'تفعيل المستخدم',
            'export' => 'تصدير بيانات المستخدمين'
        ]
    ],
    'invoices' => [
        'name' => 'إدارة الفواتير',
        'actions' => [
            'view' => 'عرض الفواتير',
            'create' => 'إنشاء فاتورة جديدة',
            'edit' => 'تعديل الفاتورة',
            'delete' => 'حذف الفاتورة',
            'export' => 'تصدير الفواتير',
            'stats' => 'عرض إحصائيات الفواتير'
        ]
    ],
    'clients' => [
        'name' => 'إدارة العملاء',
        'actions' => [
            'view' => 'عرض العملاء',
            'create' => 'إنشاء عميل جديد',
            'edit' => 'تعديل بيانات العميل',
            'delete' => 'حذف العميل',
            'export' => 'تصدير بيانات العملاء'
        ]
    ],
    'products' => [
        'name' => 'إدارة المنتجات',
        'actions' => [
            'view' => 'عرض المنتجات',
            'create' => 'إنشاء منتج جديد',
            'edit' => 'تعديل المنتج',
            'delete' => 'حذف المنتج',
            'import' => 'استيراد المنتجات',
            'export' => 'تصدير المنتجات'
        ]
    ],
    'reports' => [
        'name' => 'التقارير والتحليلات',
        'actions' => [
            'view' => 'عرض التقارير',
            'export' => 'تصدير التقارير',
            'financial' => 'التقارير المالية',
            'analytics' => 'تحليلات النظام'
        ]
    ],
    'system' => [
        'name' => 'إدارة النظام',
        'actions' => [
            'view' => 'عرض إعدادات النظام',
            'edit' => 'تعديل إعدادات النظام',
            'backup' => 'إنشاء نسخة احتياطية',
            'restore' => 'استعادة النسخة الاحتياطية',
            'maintenance' => 'وضع الصيانة'
        ]
    ],
    'templates' => [
        'name' => 'إدارة القوالب',
        'actions' => [
            'view' => 'عرض القوالب',
            'create' => 'إنشاء قالب جديد',
            'edit' => 'تعديل القالب',
            'delete' => 'حذف القالب',
            'upload' => 'رفع قالب'
        ]
    ],
    'audit' => [
        'name' => 'سجلات المراجعة',
        'actions' => [
            'view' => 'عرض سجلات المراجعة',
            'export' => 'تصدير السجلات',
            'delete' => 'حذف السجلات القديمة'
        ]
    ],
    'api' => [
        'name' => 'إدارة API',
        'actions' => [
            'view' => 'عرض مفاتيح API',
            'create' => 'إنشاء مفتاح API',
            'edit' => 'تعديل مفتاح API',
            'delete' => 'حذف مفتاح API',
            'logs' => 'عرض سجلات API'
        ]
    ],
    'admins' => [
        'name' => 'إدارة المديرين',
        'actions' => [
            'view' => 'عرض المديرين',
            'create' => 'إنشاء مدير جديد',
            'edit' => 'تعديل بيانات المدير',
            'delete' => 'حذف المدير',
            'permissions' => 'إدارة الصلاحيات'
        ]
    ]
]);

/**
 * Default Permissions for Each Role
 */
define('DEFAULT_PERMISSIONS', [
    'super_admin' => [
        'users' => ['view', 'create', 'edit', 'delete', 'suspend', 'activate', 'export'],
        'invoices' => ['view', 'create', 'edit', 'delete', 'export', 'stats'],
        'clients' => ['view', 'create', 'edit', 'delete', 'export'],
        'products' => ['view', 'create', 'edit', 'delete', 'import', 'export'],
        'reports' => ['view', 'export', 'financial', 'analytics'],
        'system' => ['view', 'edit', 'backup', 'restore', 'maintenance'],
        'templates' => ['view', 'create', 'edit', 'delete', 'upload'],
        'audit' => ['view', 'export', 'delete'],
        'api' => ['view', 'create', 'edit', 'delete', 'logs'],
        'admins' => ['view', 'create', 'edit', 'delete', 'permissions']
    ],
    'admin' => [
        'users' => ['view', 'create', 'edit', 'suspend', 'activate', 'export'],
        'invoices' => ['view', 'create', 'edit', 'export', 'stats'],
        'clients' => ['view', 'create', 'edit', 'export'],
        'products' => ['view', 'create', 'edit', 'import', 'export'],
        'reports' => ['view', 'export', 'financial', 'analytics'],
        'system' => ['view', 'edit'],
        'templates' => ['view', 'create', 'edit', 'upload'],
        'audit' => ['view', 'export'],
        'api' => ['view', 'create', 'edit']
    ],
    'moderator' => [
        'users' => ['view', 'edit', 'suspend'],
        'invoices' => ['view', 'edit', 'stats'],
        'clients' => ['view', 'edit'],
        'products' => ['view', 'edit'],
        'reports' => ['view', 'export'],
        'system' => ['view'],
        'templates' => ['view'],
        'audit' => ['view']
    ],
    'viewer' => [
        'users' => ['view'],
        'invoices' => ['view'],
        'clients' => ['view'],
        'products' => ['view'],
        'reports' => ['view'],
        'system' => ['view'],
        'templates' => ['view'],
        'audit' => ['view']
    ]
]);

/**
 * Check if a role has a specific permission
 */
function hasPermission($role, $category, $action) {
    $permissions = DEFAULT_PERMISSIONS[$role] ?? [];
    return in_array($action, $permissions[$category] ?? []);
}

/**
 * Get all permissions for a role
 */
function getRolePermissions($role) {
    return DEFAULT_PERMISSIONS[$role] ?? [];
}

/**
 * Get permission level for a role
 */
function getPermissionLevel($role) {
    return PERMISSION_LEVELS[$role]['level'] ?? 0;
}

/**
 * Check if role A can manage role B
 */
function canManageRole($roleA, $roleB) {
    return getPermissionLevel($roleA) > getPermissionLevel($roleB);
}
?>
