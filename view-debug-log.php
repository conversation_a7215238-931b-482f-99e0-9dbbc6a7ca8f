<?php
echo "<h1>🔍 Debug Log Viewer</h1>";

// Common PHP error log locations
$possible_logs = [
    'C:\xampp\php\logs\php_error_log',
    'C:\xampp\apache\logs\error.log',
    'C:\xampp\logs\php_error_log',
    ini_get('error_log'),
    'php_errors.log',
    'error.log'
];

echo "<h2>Checking possible log locations:</h2>";

$found_log = null;
foreach ($possible_logs as $log_path) {
    if ($log_path && file_exists($log_path)) {
        echo "<div style='color: green;'>✅ Found: $log_path</div>";
        if (!$found_log) $found_log = $log_path;
    } else {
        echo "<div style='color: gray;'>❌ Not found: $log_path</div>";
    }
}

if ($found_log) {
    echo "<h2>Recent Debug Messages (last 50 lines):</h2>";
    
    $lines = file($found_log);
    if ($lines) {
        $recent_lines = array_slice($lines, -50);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6;'>";
        
        foreach ($recent_lines as $line) {
            $line = htmlspecialchars($line);
            
            // Highlight our debug messages
            if (strpos($line, 'DEBUG SKU') !== false) {
                echo "<div style='background: #fff3cd; padding: 2px;'>$line</div>";
            } elseif (strpos($line, 'DEBUG HEADERS') !== false) {
                echo "<div style='background: #d1ecf1; padding: 2px;'>$line</div>";
            } else {
                echo $line;
            }
        }
        
        echo "</div>";
        
        echo "<h3>Filter Debug Messages Only:</h3>";
        $debug_lines = array_filter($recent_lines, function($line) {
            return strpos($line, 'DEBUG SKU') !== false || strpos($line, 'DEBUG HEADERS') !== false;
        });
        
        if (!empty($debug_lines)) {
            echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; font-family: monospace; white-space: pre-wrap;'>";
            foreach ($debug_lines as $line) {
                echo htmlspecialchars($line);
            }
            echo "</div>";
        } else {
            echo "<div style='color: orange;'>⚠️ No debug messages found. Try importing a file first.</div>";
        }
        
    } else {
        echo "<div style='color: red;'>❌ Could not read log file</div>";
    }
} else {
    echo "<div style='color: red;'>❌ No log file found</div>";
    echo "<h3>Manual Debug Test:</h3>";
    echo "<div>Try this to generate a debug message:</div>";
    error_log("TEST DEBUG MESSAGE: " . date('Y-m-d H:i:s'));
    echo "<div style='color: green;'>✅ Test message logged</div>";
}

echo "<h2>PHP Configuration:</h2>";
echo "<div>Error Reporting: " . (error_reporting() ? 'ON' : 'OFF') . "</div>";
echo "<div>Log Errors: " . (ini_get('log_errors') ? 'ON' : 'OFF') . "</div>";
echo "<div>Error Log: " . (ini_get('error_log') ?: 'Not set') . "</div>";
echo "<div>Display Errors: " . (ini_get('display_errors') ? 'ON' : 'OFF') . "</div>";
?>
