<?php
/**
 * Admin Login Page
 * Multi-Tenant Administrative Management System
 */

define('ADMIN_SYSTEM', true);
require_once '../config/admin_config.php';

// Redirect if already logged in
if (isAdminLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeAdminInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $pdo = getAdminDBConnection();
            
            // Check for account lockout
            $stmt = $pdo->prepare("
                SELECT id, username, email, password, first_name, last_name, role, permissions,
                       is_active, login_attempts, locked_until, two_factor_enabled
                FROM admin_users 
                WHERE (username = ? OR email = ?)
            ");
            $stmt->execute([$username, $username]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$admin) {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            } elseif (!$admin['is_active']) {
                $error = 'تم إيقاف هذا الحساب. يرجى التواصل مع مدير النظام';
            } elseif ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
                $remainingTime = ceil((strtotime($admin['locked_until']) - time()) / 60);
                $error = "تم قفل الحساب مؤقتاً. يرجى المحاولة بعد {$remainingTime} دقيقة";
            } elseif (!password_verify($password, $admin['password'])) {
                // Increment login attempts
                $attempts = $admin['login_attempts'] + 1;
                $lockUntil = null;
                
                if ($attempts >= ADMIN_MAX_LOGIN_ATTEMPTS) {
                    $lockUntil = date('Y-m-d H:i:s', time() + ADMIN_LOCKOUT_DURATION);
                    $error = 'تم تجاوز عدد المحاولات المسموحة. تم قفل الحساب مؤقتاً';
                } else {
                    $remaining = ADMIN_MAX_LOGIN_ATTEMPTS - $attempts;
                    $error = "كلمة المرور غير صحيحة. المحاولات المتبقية: {$remaining}";
                }
                
                $stmt = $pdo->prepare("
                    UPDATE admin_users 
                    SET login_attempts = ?, locked_until = ?
                    WHERE id = ?
                ");
                $stmt->execute([$attempts, $lockUntil, $admin['id']]);
                
                // Log failed login attempt
                logAdminActivity('failed_login', 'admin_users', $admin['id'], 
                    "محاولة تسجيل دخول فاشلة للمستخدم: {$admin['username']}");
                
            } else {
                // Successful login
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_last_activity'] = time();
                
                // Reset login attempts and update last login
                $stmt = $pdo->prepare("
                    UPDATE admin_users 
                    SET login_attempts = 0, locked_until = NULL, 
                        last_login = NOW(), last_login_ip = ?
                    WHERE id = ?
                ");
                $stmt->execute([$_SERVER['REMOTE_ADDR'], $admin['id']]);
                
                // Log successful login
                logAdminActivity('login', 'admin_users', $admin['id'], 
                    "تسجيل دخول ناجح للمستخدم: {$admin['username']}");
                
                // Handle remember me
                if ($remember) {
                    $token = generateSecureToken();
                    setcookie('admin_remember', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                    
                    // Store remember token in database (implement if needed)
                }
                
                // Redirect to dashboard
                $redirectUrl = $_GET['redirect'] ?? 'index.php';
                header('Location: ' . $redirectUrl);
                exit;
            }
            
        } catch (Exception $e) {
            logAdminError("Login error: " . $e->getMessage());
            $error = 'حدث خطأ في النظام. يرجى المحاولة لاحقاً';
        }
    }
}

$pageTitle = 'تسجيل الدخول - النظام الإداري';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
        }
        
        .form-control.with-icon {
            border-left: none;
        }
        
        .remember-me {
            font-size: 0.9rem;
        }
        
        .system-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            font-size: 0.85rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-shield-alt fa-2x mb-3"></i>
                <h1><?php echo ADMIN_NAME; ?></h1>
                <p class="mb-0">النظام الإداري متعدد المستأجرين</p>
            </div>
            
            <div class="login-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control with-icon" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                   required autocomplete="username">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control with-icon" id="password" name="password" 
                                   required autocomplete="current-password">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check remember-me">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            تذكرني لمدة 30 يوماً
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </div>
                </form>
                
                <div class="system-info">
                    <div class="row text-center">
                        <div class="col-6">
                            <i class="fas fa-code-branch me-1"></i>
                            الإصدار <?php echo ADMIN_VERSION; ?>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-shield-alt me-1"></i>
                            نظام آمن
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Auto-focus on username field
        document.getElementById('username').focus();
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('يرجى إدخال جميع البيانات المطلوبة');
            }
        });
    </script>
</body>
</html>
