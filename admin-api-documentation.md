# Admin Dashboard API Documentation

## Base URL
```
https://yourdomain.com/admin/api/v1
```

## Authentication

### Session Authentication (Web Dashboard)
```javascript
// Login request
POST /auth/login
{
    "username": "<EMAIL>",
    "password": "password"
}

// Response
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "super_admin",
            "permissions": {...}
        },
        "token": "jwt_token_here"
    },
    "message": "Login successful"
}
```

### API Key Authentication
```http
GET /users
X-API-Key: your_api_key
X-API-Secret: your_api_secret
```

## User Management Endpoints

### List Users
```http
GET /users?page=1&per_page=20&search=john&status=active&subscription=premium
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "username": "john_doe",
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "company_name": "Acme Corp",
            "subscription_plan": "premium",
            "is_active": true,
            "created_at": "2024-01-01T00:00:00Z",
            "last_login": "2024-01-15T10:30:00Z"
        }
    ],
    "meta": {
        "page": 1,
        "per_page": 20,
        "total": 150,
        "total_pages": 8
    }
}
```

### Get User Details
```http
GET /users/{id}
```

### Create User
```http
POST /users
Content-Type: application/json

{
    "username": "new_user",
    "email": "<EMAIL>",
    "password": "secure_password",
    "first_name": "New",
    "last_name": "User",
    "company_name": "Company Name",
    "subscription_plan": "free"
}
```

### Update User
```http
PUT /users/{id}
Content-Type: application/json

{
    "first_name": "Updated",
    "last_name": "Name",
    "subscription_plan": "premium"
}
```

### Suspend/Activate User
```http
POST /users/{id}/suspend
POST /users/{id}/activate
```

### Delete User
```http
DELETE /users/{id}
```

## Invoice Management Endpoints

### List Invoices
```http
GET /invoices?page=1&user_id=123&status=paid&date_from=2024-01-01&date_to=2024-01-31
```

### Get Invoice Details
```http
GET /invoices/{id}
```

### Update Invoice
```http
PUT /invoices/{id}
Content-Type: application/json

{
    "status": "paid",
    "notes": "Payment received"
}
```

### Delete Invoice
```http
DELETE /invoices/{id}
```

### Invoice Statistics
```http
GET /invoices/stats?period=month&year=2024
```

**Response:**
```json
{
    "success": true,
    "data": {
        "total_invoices": 1250,
        "total_revenue": 125000.50,
        "paid_invoices": 1100,
        "pending_invoices": 100,
        "overdue_invoices": 50,
        "monthly_stats": [
            {
                "month": "2024-01",
                "invoices": 100,
                "revenue": 10000.00
            }
        ]
    }
}
```

## Client Management Endpoints

### List Clients
```http
GET /clients?page=1&user_id=123&search=company&active=true
```

### Get Client Details
```http
GET /clients/{id}
```

### Update Client
```http
PUT /clients/{id}
```

### Delete Client
```http
DELETE /clients/{id}
```

## System Management Endpoints

### Dashboard Statistics
```http
GET /system/dashboard-stats
```

**Response:**
```json
{
    "success": true,
    "data": {
        "users": {
            "total": 1500,
            "active": 1200,
            "new_today": 15,
            "new_this_month": 450
        },
        "invoices": {
            "total": 12500,
            "new_today": 45,
            "total_revenue": 1250000.50,
            "revenue_today": 5500.00
        },
        "clients": {
            "total": 3200,
            "active": 2800,
            "new_today": 12
        },
        "system": {
            "disk_usage": 75.5,
            "memory_usage": 68.2,
            "cpu_load": 45.8
        }
    }
}
```

### System Settings
```http
GET /system/settings
PUT /system/settings
```

### System Logs
```http
GET /system/logs?type=error&date_from=2024-01-01&limit=100
```

### Create Backup
```http
POST /system/backup
Content-Type: application/json

{
    "type": "full",
    "name": "backup_2024_01_15"
}
```

### List Backups
```http
GET /system/backups
```

## Reports Endpoints

### User Activity Report
```http
GET /reports/user-activity?user_id=123&date_from=2024-01-01&date_to=2024-01-31
```

### Revenue Report
```http
GET /reports/revenue?period=month&year=2024&format=json
```

### System Usage Report
```http
GET /reports/system-usage?period=week
```

### Export Report
```http
GET /reports/export?type=users&format=excel&date_from=2024-01-01
```

## Audit Log Endpoints

### List Admin Activities
```http
GET /audit/activities?admin_id=1&action=user_update&date_from=2024-01-01
```

### Get Activity Details
```http
GET /audit/activities/{id}
```

## Template Management Endpoints

### List Templates
```http
GET /templates?type=invoice&active=true
```

### Get Template
```http
GET /templates/{id}
```

### Create Template
```http
POST /templates
Content-Type: application/json

{
    "name": "Modern Invoice",
    "type": "invoice",
    "html_content": "<html>...</html>",
    "css_content": "body { ... }",
    "is_active": true
}
```

### Update Template
```http
PUT /templates/{id}
```

### Delete Template
```http
DELETE /templates/{id}
```

## Error Handling

### Validation Errors (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email field is required"],
        "password": ["Password must be at least 8 characters"]
    }
}
```

### Authentication Errors (401)
```json
{
    "success": false,
    "message": "Authentication required",
    "error_code": "AUTH_REQUIRED"
}
```

### Permission Errors (403)
```json
{
    "success": false,
    "message": "Insufficient permissions",
    "error_code": "PERMISSION_DENIED"
}
```

### Rate Limit Errors (429)
```json
{
    "success": false,
    "message": "Rate limit exceeded",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "retry_after": 3600
}
```
