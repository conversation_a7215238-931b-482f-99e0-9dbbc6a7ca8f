<?php
/**
 * Admin System Installation Script
 * Sets up the multi-tenant administrative management system
 */

// Prevent direct access without proper authentication
if (!isset($_GET['install_key']) || $_GET['install_key'] !== 'admin_install_2024') {
    die('Access denied. Invalid installation key.');
}

// Include the main database configuration
require_once '../../config/database.php';

$installation_log = [];
$errors = [];

function logInstallation($message, $type = 'info') {
    global $installation_log;
    $installation_log[] = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message
    ];
}

function displayResults() {
    global $installation_log, $errors;
    
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>تثبيت النظام الإداري</title>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }";
    echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
    echo ".header { text-align: center; margin-bottom: 30px; }";
    echo ".log-entry { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".info { background: #e3f2fd; border-left: 4px solid #2196f3; }";
    echo ".success { background: #e8f5e8; border-left: 4px solid #4caf50; }";
    echo ".error { background: #ffebee; border-left: 4px solid #f44336; }";
    echo ".warning { background: #fff3e0; border-left: 4px solid #ff9800; }";
    echo ".summary { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='container'>";
    echo "<div class='header'>";
    echo "<h1>🚀 تثبيت النظام الإداري متعدد المستأجرين</h1>";
    echo "<p>نظام إدارة شامل لمنصة الفواتير SaaS</p>";
    echo "</div>";
    
    foreach ($installation_log as $entry) {
        $class = $entry['type'];
        echo "<div class='log-entry {$class}'>";
        echo "<strong>[{$entry['time']}]</strong> {$entry['message']}";
        echo "</div>";
    }
    
    echo "<div class='summary'>";
    if (empty($errors)) {
        echo "<h3 style='color: #4caf50;'>✅ تم التثبيت بنجاح!</h3>";
        echo "<p>تم إنشاء النظام الإداري بنجاح. يمكنك الآن الوصول إليه من خلال:</p>";
        echo "<ul>";
        echo "<li><strong>رابط لوحة التحكم:</strong> <a href='../dashboard/'>admin/dashboard/</a></li>";
        echo "<li><strong>اسم المستخدم:</strong> admin</li>";
        echo "<li><strong>كلمة المرور:</strong> admin123</li>";
        echo "<li><strong>البريد الإلكتروني:</strong> <EMAIL></li>";
        echo "</ul>";
        echo "<p style='color: #ff9800;'><strong>تحذير:</strong> يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.</p>";
    } else {
        echo "<h3 style='color: #f44336;'>❌ فشل في التثبيت</h3>";
        echo "<p>حدثت أخطاء أثناء التثبيت:</p>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: #f44336;'>{$error}</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    echo "</div>";
    echo "</body>";
    echo "</html>";
}

try {
    logInstallation("بدء تثبيت النظام الإداري متعدد المستأجرين", 'info');
    
    // Create database connection
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    logInstallation("تم الاتصال بقاعدة البيانات بنجاح", 'success');
    
    // Read and execute admin schema
    $schemaFile = '../database/admin_schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("ملف قاعدة البيانات غير موجود: {$schemaFile}");
    }
    
    $schema = file_get_contents($schemaFile);
    if (!$schema) {
        throw new Exception("فشل في قراءة ملف قاعدة البيانات");
    }
    
    logInstallation("تم قراءة ملف قاعدة البيانات", 'success');
    
    // Split SQL statements and execute them
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // Log specific table creation
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                logInstallation("تم إنشاء جدول: {$matches[1]}", 'success');
            } elseif (preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                logInstallation("تم إدراج البيانات في جدول: {$matches[1]}", 'success');
            }
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                logInstallation("الجدول موجود مسبقاً - تم التخطي", 'warning');
            } else {
                throw $e;
            }
        }
    }
    
    // Create admin directories
    $directories = [
        '../config',
        '../api/v1/auth',
        '../api/v1/users',
        '../api/v1/invoices',
        '../api/v1/clients',
        '../api/v1/products',
        '../api/v1/reports',
        '../api/v1/system',
        '../api/middleware',
        '../dashboard/users',
        '../dashboard/invoices',
        '../dashboard/clients',
        '../dashboard/products',
        '../dashboard/reports',
        '../dashboard/system',
        '../dashboard/audit',
        '../includes',
        '../assets/css',
        '../assets/js',
        '../assets/images',
        '../logs'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                logInstallation("تم إنشاء مجلد: {$dir}", 'success');
            } else {
                logInstallation("فشل في إنشاء مجلد: {$dir}", 'error');
                $errors[] = "فشل في إنشاء مجلد: {$dir}";
            }
        } else {
            logInstallation("المجلد موجود مسبقاً: {$dir}", 'info');
        }
    }
    
    // Create .htaccess for security
    $htaccessContent = "# Admin System Security\n";
    $htaccessContent .= "RewriteEngine On\n";
    $htaccessContent .= "RewriteCond %{REQUEST_FILENAME} !-f\n";
    $htaccessContent .= "RewriteCond %{REQUEST_FILENAME} !-d\n";
    $htaccessContent .= "RewriteRule ^api/(.*)$ api/index.php [QSA,L]\n\n";
    $htaccessContent .= "# Deny access to sensitive files\n";
    $htaccessContent .= "<Files ~ \"\\.(log|sql|md)$\">\n";
    $htaccessContent .= "    Order allow,deny\n";
    $htaccessContent .= "    Deny from all\n";
    $htaccessContent .= "</Files>\n";
    
    if (file_put_contents('../.htaccess', $htaccessContent)) {
        logInstallation("تم إنشاء ملف .htaccess للأمان", 'success');
    }
    
    // Verify admin user creation
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn();
    
    if ($adminExists) {
        logInstallation("تم إنشاء المستخدم الإداري الافتراضي", 'success');
    } else {
        logInstallation("فشل في إنشاء المستخدم الإداري", 'error');
        $errors[] = "فشل في إنشاء المستخدم الإداري";
    }
    
    // Create installation completion marker
    $installMarker = '../.installed';
    if (file_put_contents($installMarker, date('Y-m-d H:i:s'))) {
        logInstallation("تم إنشاء علامة اكتمال التثبيت", 'success');
    }
    
    logInstallation("تم الانتهاء من تثبيت النظام الإداري بنجاح!", 'success');
    
} catch (Exception $e) {
    logInstallation("خطأ في التثبيت: " . $e->getMessage(), 'error');
    $errors[] = $e->getMessage();
}

// Display results
displayResults();
?>
