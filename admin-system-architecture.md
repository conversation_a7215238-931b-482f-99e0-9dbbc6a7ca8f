# Admin Dashboard System Architecture

## Overview
This document outlines the architecture for the administrative management system that will control and manage the existing invoice generation SaaS website.

## System Architecture

### 1. Directory Structure
```
admin/
├── config/
│   ├── admin_config.php          # Admin-specific configuration
│   ├── api_config.php            # API configuration
│   └── permissions.php           # Permission definitions
├── api/
│   ├── v1/
│   │   ├── auth/                 # Authentication endpoints
│   │   ├── users/                # User management endpoints
│   │   ├── invoices/             # Invoice management endpoints
│   │   ├── clients/              # Client management endpoints
│   │   ├── products/             # Product management endpoints
│   │   ├── reports/              # Reports and analytics endpoints
│   │   ├── system/               # System management endpoints
│   │   └── templates/            # Template management endpoints
│   └── middleware/
│       ├── auth.php              # API authentication middleware
│       ├── permissions.php       # Permission checking middleware
│       └── rate_limit.php        # Rate limiting middleware
├── dashboard/
│   ├── index.php                 # Main admin dashboard
│   ├── login.php                 # Admin login page
│   ├── users/                    # User management interface
│   ├── invoices/                 # Invoice management interface
│   ├── clients/                  # Client management interface
│   ├── products/                 # Product management interface
│   ├── reports/                  # Reports and analytics interface
│   ├── system/                   # System settings interface
│   ├── templates/                # Template management interface
│   └── audit/                    # Audit logs interface
├── includes/
│   ├── admin_functions.php       # Admin-specific functions
│   ├── api_functions.php         # API helper functions
│   ├── admin_header.php          # Admin dashboard header
│   ├── admin_footer.php          # Admin dashboard footer
│   └── admin_sidebar.php         # Admin dashboard sidebar
├── assets/
│   ├── css/                      # Admin dashboard styles
│   ├── js/                       # Admin dashboard scripts
│   └── images/                   # Admin dashboard images
└── logs/
    ├── api_access.log            # API access logs
    ├── admin_actions.log         # Admin action logs
    └── system_errors.log         # System error logs
```

### 2. Database Schema Extensions

#### Admin Users Table
```sql
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### API Keys Table
```sql
CREATE TABLE api_keys (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_user_id INT NOT NULL,
    key_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    api_secret VARCHAR(255) NOT NULL,
    permissions JSON,
    rate_limit INT DEFAULT 1000,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    last_used TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_api_key (api_key),
    INDEX idx_admin_user_id (admin_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Admin Activity Logs Table
```sql
CREATE TABLE admin_activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    old_values JSON,
    new_values JSON,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    api_endpoint VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_admin_user_id (admin_user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. Permission System

#### Permission Levels
- **super_admin**: Full system access, can manage other admins
- **admin**: Full access to user data and system settings
- **moderator**: Limited access to user data, read-only system settings

#### Permission Categories
- **users**: manage_users, view_users, delete_users, suspend_users
- **invoices**: manage_invoices, view_invoices, delete_invoices
- **clients**: manage_clients, view_clients, delete_clients
- **products**: manage_products, view_products, delete_products
- **reports**: view_reports, export_reports
- **system**: manage_settings, view_logs, manage_templates
- **billing**: manage_subscriptions, view_payments

### 4. API Architecture

#### Authentication Methods
1. **Session-based**: For admin dashboard interface
2. **API Key**: For programmatic access
3. **JWT Token**: For mobile/external applications

#### API Endpoints Structure
```
/api/v1/auth/
    POST /login          # Admin login
    POST /logout         # Admin logout
    POST /refresh        # Refresh token

/api/v1/users/
    GET /                # List all users
    GET /{id}            # Get user details
    POST /               # Create user
    PUT /{id}            # Update user
    DELETE /{id}         # Delete user
    POST /{id}/suspend   # Suspend user
    POST /{id}/activate  # Activate user

/api/v1/invoices/
    GET /                # List all invoices
    GET /{id}            # Get invoice details
    PUT /{id}            # Update invoice
    DELETE /{id}         # Delete invoice
    GET /stats           # Invoice statistics

/api/v1/system/
    GET /stats           # System statistics
    GET /settings        # Get system settings
    PUT /settings        # Update system settings
    POST /backup         # Create backup
    GET /logs            # Get system logs
```

### 5. Security Measures

#### Authentication Security
- Strong password requirements for admin users
- Two-factor authentication (optional)
- Session timeout and management
- API rate limiting
- IP whitelisting for admin access

#### Data Security
- All API communications over HTTPS
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection for forms

#### Audit Trail
- Complete logging of all admin actions
- Data change tracking (old vs new values)
- API access logging
- Failed login attempt tracking

### 6. Integration Points

#### Database Integration
- Shared database with main invoice system
- Read/write access to all main system tables
- Additional admin-specific tables

#### File System Integration
- Access to main system uploads and templates
- Backup and restore capabilities
- Log file management

### 7. Key Features Implementation

#### User Management
- Complete CRUD operations for users
- Bulk operations (suspend, activate, delete)
- User activity monitoring
- Subscription management

#### System Monitoring
- Real-time dashboard with key metrics
- Performance monitoring
- Error tracking and alerting
- Usage analytics

#### Content Management
- Invoice template management
- Email template management
- System settings configuration
- Backup and restore utilities

This architecture provides a comprehensive foundation for building the admin dashboard system with proper security, scalability, and maintainability.

## 8. API Documentation

### Authentication
All API requests require authentication via one of these methods:
- **Session Cookie**: For web dashboard requests
- **API Key**: Include `X-API-Key` and `X-API-Secret` headers
- **Bearer Token**: Include `Authorization: Bearer <token>` header

### Response Format
All API responses follow this structure:
```json
{
    "success": true|false,
    "data": {...},
    "message": "Success/Error message",
    "errors": [...],
    "meta": {
        "page": 1,
        "per_page": 20,
        "total": 100,
        "total_pages": 5
    }
}
```

### Error Codes
- **200**: Success
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **429**: Rate Limit Exceeded
- **500**: Internal Server Error

### Rate Limiting
- Default: 1000 requests per hour per API key
- Headers included in response:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

## 9. Implementation Phases

### Phase 1: Core Infrastructure (Week 1)
- Database setup and migrations
- Basic authentication system
- API framework and middleware
- Admin dashboard layout

### Phase 2: User Management (Week 2)
- User CRUD operations
- User search and filtering
- Bulk operations
- User activity tracking

### Phase 3: Data Management (Week 3)
- Invoice management interface
- Client management interface
- Product management interface
- Template management

### Phase 4: System Features (Week 4)
- System monitoring dashboard
- Reports and analytics
- Backup and restore
- Audit logging

### Phase 5: Advanced Features (Week 5)
- API key management
- Advanced permissions
- Email notifications
- System optimization

## 10. Security Checklist

### Authentication & Authorization
- [ ] Strong password requirements
- [ ] Session management
- [ ] API key security
- [ ] Permission-based access control
- [ ] Failed login attempt tracking

### Data Protection
- [ ] Input validation and sanitization
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Data encryption for sensitive fields

### API Security
- [ ] Rate limiting
- [ ] Request/response logging
- [ ] IP whitelisting (optional)
- [ ] HTTPS enforcement
- [ ] API versioning

### Monitoring & Logging
- [ ] Complete audit trail
- [ ] Error logging
- [ ] Performance monitoring
- [ ] Security event alerts
- [ ] Regular security reviews
