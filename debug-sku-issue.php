<?php
require_once 'includes/SafeFileReader.php';

echo "<h2>تشخيص مشكلة SKU</h2>";

// اختبار ملف CSV التجريبي
$test_file = 'sample-products-test.csv';

if (!file_exists($test_file)) {
    echo "<p style='color: red;'>❌ الملف غير موجود: $test_file</p>";
    exit;
}

echo "<h3>1. محتوى الملف الخام:</h3>";
$content = file_get_contents($test_file);
echo "<pre>" . htmlspecialchars($content) . "</pre>";

echo "<h3>2. قراءة الملف باستخدام SafeFileReader:</h3>";
try {
    $reader = new SafeFileReader($test_file, 'sample-products-test.csv');
    $reader->validateFile();
    $rows = $reader->readFile();
    
    echo "<p>✅ تم قراءة الملف بنجاح</p>";
    echo "<p>📊 عدد الصفوف: " . count($rows) . "</p>";
    
    if (count($rows) > 0) {
        echo "<h4>العناوين (الصف الأول):</h4>";
        echo "<pre>";
        foreach ($rows[0] as $index => $header) {
            echo "[$index] '" . htmlspecialchars($header) . "'\n";
        }
        echo "</pre>";
        
        if (count($rows) > 1) {
            echo "<h4>أول منتج (الصف الثاني):</h4>";
            echo "<pre>";
            foreach ($rows[1] as $index => $value) {
                echo "[$index] '" . htmlspecialchars($value) . "'\n";
            }
            echo "</pre>";
            
            // البحث عن عمود SKU
            $sku_column = -1;
            foreach ($rows[0] as $index => $header) {
                if (strtolower(trim($header)) === 'sku') {
                    $sku_column = $index;
                    break;
                }
            }
            
            echo "<h4>تحليل عمود SKU:</h4>";
            if ($sku_column >= 0) {
                echo "<p>✅ تم العثور على عمود SKU في الموضع: $sku_column</p>";
                $sku_value = isset($rows[1][$sku_column]) ? $rows[1][$sku_column] : '';
                echo "<p>قيمة SKU للمنتج الأول: '" . htmlspecialchars($sku_value) . "'</p>";
                echo "<p>هل SKU فارغ؟ " . (empty($sku_value) ? "نعم ❌" : "لا ✅") . "</p>";
                echo "<p>طول SKU: " . strlen($sku_value) . "</p>";
            } else {
                echo "<p>❌ لم يتم العثور على عمود SKU</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قراءة الملف: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>3. محاكاة منطق import-products.php:</h3>";

// محاكاة منطق التحقق من العناوين
$headers = array_map(function($header) {
    $cleaned = trim($header);
    $cleaned = preg_replace('/\s+/', ' ', $cleaned);
    $cleaned = strtolower($cleaned);
    return $cleaned;
}, $rows[0]);

echo "<h4>العناوين بعد التنظيف:</h4>";
echo "<pre>";
foreach ($headers as $index => $header) {
    echo "[$index] '" . htmlspecialchars($header) . "'\n";
}
echo "</pre>";

// خريطة العناوين
$header_map = [
    'name' => ['name', 'اسم المنتج', 'product name', 'اسم', 'المنتج'],
    'price' => ['price', 'السعر', 'سعر', 'cost', 'amount'],
    'quantity' => ['quantity', 'الكمية', 'كمية', 'qty', 'stock'],
    'description' => ['description', 'الوصف', 'وصف', 'desc'],
    'category' => ['category', 'الفئة', 'فئة', 'cat'],
    'sku' => ['sku', 'رمز المنتج', 'رمز', 'code', 'product code'],
    'status' => ['status', 'الحالة', 'حالة', 'state']
];

$found_headers = [];
$header_positions = [];

foreach ($header_map as $standard_name => $alternatives) {
    $found = false;
    foreach ($alternatives as $alt) {
        $alt_cleaned = strtolower(trim($alt));
        foreach ($headers as $position => $header) {
            if ($header === $alt_cleaned) {
                $found_headers[$standard_name] = $position;
                $header_positions[$position] = $standard_name;
                $found = true;
                break 2;
            }
        }
    }
}

echo "<h4>العناوين المطابقة:</h4>";
echo "<pre>";
foreach ($found_headers as $standard => $position) {
    echo "$standard => موضع $position\n";
}
echo "</pre>";

// التحقق من العناوين المطلوبة
$required_headers = ['name', 'price', 'quantity', 'sku'];
$missing_headers = [];
foreach ($required_headers as $header) {
    if (!isset($found_headers[$header])) {
        $missing_headers[] = $header;
    }
}

if (!empty($missing_headers)) {
    echo "<p style='color: red;'>❌ العناوين المطلوبة مفقودة: " . implode(', ', $missing_headers) . "</p>";
} else {
    echo "<p style='color: green;'>✅ جميع العناوين المطلوبة موجودة</p>";
}

// اختبار معالجة أول منتج
if (count($rows) > 1 && empty($missing_headers)) {
    echo "<h4>معالجة أول منتج:</h4>";
    $row = $rows[1];
    $product = [];
    
    foreach ($found_headers as $standard_name => $position) {
        $value = isset($row[$position]) ? trim($row[$position]) : '';
        
        if ($standard_name === 'sku') {
            $cleaned_sku = trim($value);
            $product['sku'] = !empty($cleaned_sku) ? $cleaned_sku : '';
            $product['sku_source'] = !empty($cleaned_sku) ? 'provided' : 'missing';
            
            echo "<p>قيمة SKU الخام: '" . htmlspecialchars($value) . "'</p>";
            echo "<p>قيمة SKU بعد التنظيف: '" . htmlspecialchars($cleaned_sku) . "'</p>";
            echo "<p>SKU في المصفوفة: '" . htmlspecialchars($product['sku']) . "'</p>";
            echo "<p>مصدر SKU: " . $product['sku_source'] . "</p>";
            echo "<p>هل SKU فارغ؟ " . (empty($product['sku']) ? "نعم ❌" : "لا ✅") . "</p>";
        } else {
            $product[$standard_name] = $value;
        }
    }
    
    echo "<h4>بيانات المنتج النهائية:</h4>";
    echo "<pre>" . htmlspecialchars(print_r($product, true)) . "</pre>";
}
?>
